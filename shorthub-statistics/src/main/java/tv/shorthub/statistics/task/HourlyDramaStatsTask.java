 package tv.shorthub.statistics.task;

 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Component;
 import tv.shorthub.statistics.service.IHourlyDramaStatsService;

 /**
  * 每小时剧集统计定时任务
  *
  * <AUTHOR>
  * @date 2025-07-11
  */
 @Slf4j
 @Component
 public class HourlyDramaStatsTask {

     @Autowired
     private IHourlyDramaStatsService hourlyDramaStatsService;

     /**
      * 每10分钟执行一次，更新当前小时的剧集统计数据
      * cron表达式: 0/30 * * * * ? 表示每30秒执行一次（开发测试用）
      * 生产环境建议: 0 0/10 * * * ? （每10分钟执行一次）
      */
     @Scheduled(cron = "0/30 * * * * ?")
     public void updateCurrentHourDramaStats() {
         log.info("[剧集统计定时任务] 开始执行每小时剧集统计任务");

         try {
             // 统计当前小时的剧集数据
             hourlyDramaStatsService.calculateAndSaveCurrentHourStats();
             log.info("[剧集统计定时任务] 当前小时剧集统计任务执行成功");
         } catch (Exception e) {
             log.error("[剧集统计定时任务] 当前小时剧集统计任务执行失败", e);
         }
     }

     /**
      * 每天凌晨2点执行，重新统计前一天的剧集数据
      * cron表达式: 0 0 2 * * ? 表示每天凌晨2点执行
      */
     @Scheduled(cron = "0 0 2 * * ?")
     public void recalculateYesterdayDramaStats() {
         log.info("[剧集统计定时任务] 开始执行昨天剧集数据重新统计任务");

         try {
             // 重新统计昨天的剧集数据
             hourlyDramaStatsService.recalculateYesterdayStats();
             log.info("[剧集统计定时任务] 昨天剧集数据重新统计任务执行成功");
         } catch (Exception e) {
             log.error("[剧集统计定时任务] 昨天剧集数据重新统计任务执行失败", e);
         }
     }

     /**
      * 每周日凌晨3点执行，重新统计最近7天的剧集数据
      * cron表达式: 0 0 3 ? * SUN 表示每周日凌晨3点执行
      */
     @Scheduled(cron = "0 0 3 ? * SUN")
     public void recalculateWeeklyDramaStats() {
         log.info("[剧集统计定时任务] 开始执行最近7天剧集数据重新统计任务");

         try {
             // 重新统计最近7天的剧集数据
             hourlyDramaStatsService.recalculateWeeklyStats();
             log.info("[剧集统计定时任务] 最近7天剧集数据重新统计任务执行成功");
         } catch (Exception e) {
             log.error("[剧集统计定时任务] 最近7天剧集数据重新统计任务执行失败", e);
         }
     }
 }
