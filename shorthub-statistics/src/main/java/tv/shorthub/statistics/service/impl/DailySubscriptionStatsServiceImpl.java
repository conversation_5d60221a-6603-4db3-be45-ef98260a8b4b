package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.service.IDailySubscriptionStatsService;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.system.domain.StatisticsDailySubscriptionStats;
import tv.shorthub.system.service.IStatisticsDailySubscriptionStatsService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.config.StatisticsConfig;
import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 每日订阅统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Service
public class DailySubscriptionStatsServiceImpl implements IDailySubscriptionStatsService {

    @Autowired
    private IStatisticsDailySubscriptionStatsService statisticsDailySubscriptionStatsService;

    @Autowired
    private StatisticsConfig statisticsConfig;

    @Override
    public void updateCurrentDaySubscriptionStats() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDay = calendar.getTime();
        log.info("[订阅日统计定时任务] 开始统计当前日期订阅数据 - {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay));
        updateDailySubscriptionStats(currentDay);
    }

    @Override
    public void updateDailySubscriptionStats(Date statDate) {
        try {
            log.info("[订阅日统计定时任务] 开始统计日期: {}", new SimpleDateFormat("yyyy-MM-dd").format(statDate));
            
            // 使用配置类获取支持的时区列表
            List<String> timezones = statisticsConfig.getSupportedTimezones();
            
            for (String timezone : timezones) {
                try {
                    // 为每个时区计算该时区的"一天"对应的UTC+8时间范围
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(statDate);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MILLISECOND, 0);
                    Date timezoneStartTime = calendar.getTime();
                    calendar.add(Calendar.DATE, 1);
                    Date timezoneEndTime = calendar.getTime();
                    
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(timezoneStartTime, timezoneEndTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];
                    
                    log.debug("[订阅日统计定时任务] 时区 {} - 查询时间范围: {} 到 {}", 
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));
                    
                    // 获取时区偏移量
                    int timezoneOffset = TimeZoneUtils.getTimezoneOffset(timezone);
                    
                    List<StatisticsDailySubscriptionStats> dailyStats = statisticsDailySubscriptionStatsService.getMapper().selectDailySubscriptionStatsWithTimezone(adjustedStartTime, adjustedEndTime, timezone, timezoneOffset);
                    if (dailyStats != null && !dailyStats.isEmpty()) {
                        statisticsDailySubscriptionStatsService.getMapper().batchInsertOrUpdate(dailyStats);
                        log.info("[订阅日统计定时任务] 成功更新 {} 条订阅统计数据 (时区: {})", dailyStats.size(), timezone);
                    } else {
                        log.info("[订阅日统计定时任务] 当前日期无订阅数据 (时区: {})", timezone);
                    }
                } catch (Exception e) {
                    log.error("[订阅日统计定时任务] 统计失败 (时区: {}): {}", timezone, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("[订阅日统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    private List<StatisticsDailySubscriptionStats> executeStatisticsQueryWithTimezone(Date startTime, Date endTime, String timezone) {
        try {
            // 根据目标时区调整查询时间范围
            Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
            Date adjustedStartTime = adjustedTimeRange[0];
            Date adjustedEndTime = adjustedTimeRange[1];
            
            log.debug("[订阅日统计定时任务] 时区 {} - 原始时间范围: {} 到 {}", 
                timezone, 
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
            log.debug("[订阅日统计定时任务] 时区 {} - 调整后时间范围: {} 到 {}", 
                timezone,
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));
            
            List<StatisticsDailySubscriptionStats> result = statisticsDailySubscriptionStatsService.getMapper().selectDailySubscriptionStatsWithTimezone(adjustedStartTime, adjustedEndTime, timezone);
            log.info("[订阅日统计定时任务] 查询到 {} 条统计数据 (时区: {})", result != null ? result.size() : 0, timezone);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("[订阅日统计定时任务] 查询统计数据失败 (时区: {}): {}", timezone, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public String batchUpdateSubscriptionStats(Date startDate, Date endDate) {
        try {
            log.info("[订阅日统计批量任务] 开始全量统计订阅数据 - 时间范围: {} 到 {}", new SimpleDateFormat("yyyy-MM-dd").format(startDate), new SimpleDateFormat("yyyy-MM-dd").format(endDate));
            int totalDays = 0;
            int successDays = 0;
            int totalRecords = 0;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(endDate);
            endCalendar.set(Calendar.HOUR_OF_DAY, 0);
            endCalendar.set(Calendar.MINUTE, 0);
            endCalendar.set(Calendar.SECOND, 0);
            endCalendar.set(Calendar.MILLISECOND, 0);
            
            // 使用配置类获取支持的时区列表
            List<String> timezones = statisticsConfig.getSupportedTimezones();
            
            while (!calendar.getTime().after(endCalendar.getTime())) {
                try {
                    Date currentDay = calendar.getTime();
                    Date nextDay = DateUtils.addDays(currentDay, 1);
                    
                    for (String timezone : timezones) {
                        try {
                            // 根据目标时区调整查询时间范围
                            Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(currentDay, nextDay, timezone);
                            Date adjustedStartTime = adjustedTimeRange[0];
                            Date adjustedEndTime = adjustedTimeRange[1];
                            
                            // 获取时区偏移量
                    int timezoneOffset = TimeZoneUtils.getTimezoneOffset(timezone);
                    
                    List<StatisticsDailySubscriptionStats> dailyStats = statisticsDailySubscriptionStatsService.getMapper().selectDailySubscriptionStatsWithTimezone(adjustedStartTime, adjustedEndTime, timezone, timezoneOffset);
                            if (dailyStats != null && !dailyStats.isEmpty()) {
                                statisticsDailySubscriptionStatsService.getMapper().batchInsertOrUpdate(dailyStats);
                                totalRecords += dailyStats.size();
                            }
                        } catch (Exception e) {
                            log.error("[订阅日统计批量任务] 统计日期 {} 时区 {} 失败: {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay), timezone, e.getMessage());
                        }
                    }
                    successDays++;
                    totalDays++;
                    if (totalDays % 30 == 0) {
                        log.info("[订阅日统计批量任务] 进度: {}/{} 天，已更新 {} 条记录", successDays, totalDays, totalRecords);
                    }
                } catch (Exception e) {
                    log.error("[订阅日统计批量任务] 统计日期 {} 失败: {}", new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()), e.getMessage());
                }
                calendar.add(Calendar.DATE, 1);
            }
            String result = String.format("订阅日统计完成！共处理 %d 天，成功 %d 天，更新 %d 条记录", totalDays, successDays, totalRecords);
            log.info("[订阅日统计批量任务] {}", result);
            return result;
        } catch (Exception e) {
            String error = "订阅日批量统计失败: " + e.getMessage();
            log.error("[订阅日统计批量任务] {}", error, e);
            return error;
        }
    }

    @Override
    public String batchUpdateRecentSubscriptionStats(int days) {
        Date endDate = new Date();
        Date startDate = DateUtils.addDays(endDate, -days);
        return batchUpdateSubscriptionStats(startDate, endDate);
    }

    @Override
    public String batchUpdateAllStats() {
        log.info("[订阅日全量统计] 开始执行...");
        Map<String, Date> timeRange = statisticsDailySubscriptionStatsService.getMapper().selectTimeRange();
        Date minDate = timeRange.get("min_date");
        Date maxDate = timeRange.get("max_date");
        if (minDate == null || maxDate == null) {
            return "[订阅日全量统计] 未找到任何订阅数据，任务结束。";
        }
        log.info("[订阅日全量统计] 数据时间范围: {} 到 {}", minDate, maxDate);
        return batchUpdateSubscriptionStats(minDate, maxDate);
    }
} 