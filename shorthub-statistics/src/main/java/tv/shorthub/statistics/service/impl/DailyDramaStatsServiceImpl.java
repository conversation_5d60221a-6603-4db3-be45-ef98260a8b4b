package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.service.IDailyDramaStatsService;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import tv.shorthub.system.service.IStatisticsDailyDramaStatsService;
import tv.shorthub.statistics.config.StatisticsConfig;

import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 每日剧集统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Service
public class DailyDramaStatsServiceImpl implements IDailyDramaStatsService {

    @Autowired
    private IStatisticsDailyDramaStatsService statisticsDailyDramaStatsService;

    @Autowired
    private StatisticsConfig statisticsConfig;

    @Override
    public void updateCurrentDayDramaStats() {
        // 获取当前日期的0点
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDay = calendar.getTime();

        log.info("[剧集日统计定时任务] 开始统计当前日期剧集数据 - {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay));
        updateDailyDramaStats(currentDay);
    }

    @Override
    public void updateDailyDramaStats(Date statDate) {
        try {
            log.info("[剧集日统计定时任务] 开始统计日期: {}", new SimpleDateFormat("yyyy-MM-dd").format(statDate));

            // 支持多时区统计
            List<StatisticsDailyDramaStats> allResults = new ArrayList<>();
            List<String> timezones = statisticsConfig.getSupportedTimezones();
            
            for (String timezone : timezones) {
                try {
                    // 为每个时区计算该时区的"一天"对应的UTC+8时间范围
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(statDate);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MILLISECOND, 0);
                    Date timezoneStartTime = calendar.getTime();
                    calendar.add(Calendar.DATE, 1);
                    Date timezoneEndTime = calendar.getTime();
                    
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(timezoneStartTime, timezoneEndTime, timezone);
                    Date adjustedStartDate = adjustedTimeRange[0];
                    Date adjustedEndDate = adjustedTimeRange[1];
                    
                    log.debug("[剧集日统计定时任务] 时区 {} - 查询时间范围: {} 到 {}", 
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartDate),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndDate));
                    
                    List<StatisticsDailyDramaStats> result = statisticsDailyDramaStatsService.selectDailyDramaStats(adjustedStartDate, adjustedEndDate, timezone);
                    if (result != null && !result.isEmpty()) {
                        allResults.addAll(result);
                        log.info("[剧集日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[剧集日统计定时任务] 时区 {} 统计失败: {}", timezone, e.getMessage(), e);
                }
            }

            if (!allResults.isEmpty()) {
                statisticsDailyDramaStatsService.getMapper().batchInsertOrUpdate(allResults);
                log.info("[剧集日统计定时任务] 成功更新 {} 条剧集统计数据", allResults.size());
            } else {
                log.info("[剧集日统计定时任务] 当前日期无剧集数据");
            }
        } catch (Exception e) {
            log.error("[剧集日统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String batchUpdateRecentDramaStats(int days) {
        log.info("[剧集日统计定时任务] 开始批量更新最近 {} 天的剧集统计数据", days);
        
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < days; i++) {
            Date statDate = calendar.getTime();
            updateDailyDramaStats(statDate);
            calendar.add(Calendar.DATE, -1);
        }
        
        log.info("[剧集日统计定时任务] 批量更新最近 {} 天的剧集统计数据完成", days);
        return "批量更新最近 " + days + " 天的剧集统计数据完成";
    }

    @Override
    public String batchUpdateDramaStats(Date startDate, Date endDate) {
        log.info("[剧集日统计定时任务] 开始批量更新指定时间范围的剧集统计数据: {} 到 {}", startDate, endDate);
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        
        int dayCount = 0;
        while (!calendar.getTime().after(endDate)) {
            Date statDate = calendar.getTime();
            updateDailyDramaStats(statDate);
            calendar.add(Calendar.DATE, 1);
            dayCount++;
        }
        
        log.info("[剧集日统计定时任务] 批量更新指定时间范围的剧集统计数据完成，共处理 {} 天", dayCount);
        return "批量更新指定时间范围的剧集统计数据完成，共处理 " + dayCount + " 天";
    }

    @Override
    public String batchUpdateAllStats() {
        log.info("[剧集日统计定时任务] 开始全量重算所有历史剧集统计数据");
        
        try {
            // 获取数据的时间范围
            Date startDate = new Date(System.currentTimeMillis() - 365L * 24 * 60 * 60 * 1000); // 最近一年
            Date endDate = new Date();
            
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            
            int processedDays = 0;
            while (calendar.getTime().before(endDate)) {
                Date statDate = calendar.getTime();
                updateDailyDramaStats(statDate);
                calendar.add(Calendar.DATE, 1);
                processedDays++;
            }
            
            String result = String.format("全量重算完成，处理了 %d 天的数据", processedDays);
            log.info("[剧集日统计定时任务] {}", result);
            return result;
        } catch (Exception e) {
            String errorMsg = "全量重算失败: " + e.getMessage();
            log.error("[剧集日统计定时任务] {}", errorMsg, e);
            return errorMsg;
        }
    }
}