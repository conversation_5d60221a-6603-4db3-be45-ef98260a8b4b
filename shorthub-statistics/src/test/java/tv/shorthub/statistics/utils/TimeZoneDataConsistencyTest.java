package tv.shorthub.statistics.utils;

import org.junit.Test;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时区数据一致性测试
 * 验证不同时区统计的数据汇总是否一致
 */
public class TimeZoneDataConsistencyTest {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testDataConsistency() {
        System.out.println("=== 时区数据一致性分析 ===");
        
        // 模拟定时任务统计 2025-08-01 这一天
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.AUGUST, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime(); // 2025-08-01 00:00:00
        
        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime(); // 2025-08-02 00:00:00
        
        System.out.println("定时任务统计日期：2025-08-01");
        System.out.println("原始时间范围（系统时间 UTC+8）:");
        System.out.println("  开始: " + sdf.format(startTime));
        System.out.println("  结束: " + sdf.format(endTime));
        
        // 测试关键时区
        String[] timezones = {"UTC+8", "UTC-8", "UTC+0"};
        
        for (String timezone : timezones) {
            Date[] adjusted = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
            System.out.println("\n" + timezone + " 时区:");
            System.out.println("  查询时间范围: " + sdf.format(adjusted[0]) + " 到 " + sdf.format(adjusted[1]));
            
            // 分析这个时间范围覆盖的数据
            analyzeDataCoverage(timezone, adjusted[0], adjusted[1]);
        }
        
        System.out.println("\n=== 问题分析 ===");
        System.out.println("如果所有时区的汇总数据不一致，可能的原因：");
        System.out.println("1. 不同时区查询的时间范围有重叠或遗漏");
        System.out.println("2. 时区转换逻辑导致数据边界问题");
        System.out.println("3. 订单数据在时间边界处的分布不均匀");
    }
    
    private void analyzeDataCoverage(String timezone, Date startTime, Date endTime) {
        long startHour = startTime.getTime() / (1000 * 60 * 60);
        long endHour = endTime.getTime() / (1000 * 60 * 60);
        long hoursCovered = endHour - startHour;
        
        System.out.println("    覆盖时间长度: " + hoursCovered + " 小时");
        
        // 分析可能的数据重叠或遗漏
        if ("UTC+8".equals(timezone)) {
            System.out.println("    分析: 查询系统本地时间，应该包含完整的一天数据");
        } else if ("UTC-8".equals(timezone)) {
            System.out.println("    分析: 查询时间偏移了16小时，可能丢失或重复某些时间段的数据");
        } else if ("UTC+0".equals(timezone)) {
            System.out.println("    分析: 查询时间偏移了8小时，可能丢失或重复某些时间段的数据");
        }
    }
    
    @Test
    public void testProposedSolution() {
        System.out.println("\n=== 建议的解决方案测试 ===");
        
        System.out.println("方案1: 所有时区查询相同的时间范围");
        System.out.println("  - 优点: 数据一致性好，汇总结果相同");
        System.out.println("  - 缺点: 不符合真实的时区业务需求");
        
        System.out.println("\n方案2: 修复时区转换逻辑，确保数据完整性");
        System.out.println("  - 需要确保每个时区查询的24小时数据是完整的");
        System.out.println("  - 避免数据重叠或遗漏");
        
        System.out.println("\n方案3: 重新设计时区统计逻辑");
        System.out.println("  - 基于订单的创建时间进行时区转换");
        System.out.println("  - 而不是基于查询时间范围进行转换");
    }
    
    @Test
    public void testCurrentLogicIssues() {
        System.out.println("\n=== 当前逻辑问题分析 ===");
        
        // 分析为什么UTC-8的数据会少
        System.out.println("问题：UTC-8时区的汇总数据比UTC+8少");
        System.out.println("原因分析：");
        
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.AUGUST, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();
        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime();
        
        // UTC+8 查询范围
        Date[] utc8Range = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, "UTC+8");
        System.out.println("UTC+8 查询: " + sdf.format(utc8Range[0]) + " 到 " + sdf.format(utc8Range[1]));
        
        // UTC-8 查询范围
        Date[] utcMinus8Range = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, "UTC-8");
        System.out.println("UTC-8 查询: " + sdf.format(utcMinus8Range[0]) + " 到 " + sdf.format(utcMinus8Range[1]));
        
        // 计算时间差异
        long utc8Hours = (utc8Range[1].getTime() - utc8Range[0].getTime()) / (1000 * 60 * 60);
        long utcMinus8Hours = (utcMinus8Range[1].getTime() - utcMinus8Range[0].getTime()) / (1000 * 60 * 60);
        
        System.out.println("UTC+8 查询时长: " + utc8Hours + " 小时");
        System.out.println("UTC-8 查询时长: " + utcMinus8Hours + " 小时");
        
        if (utc8Hours != utcMinus8Hours) {
            System.out.println("❌ 问题：不同时区查询的时间长度不一致！");
        } else {
            System.out.println("✅ 查询时间长度一致，问题可能在数据分布上");
        }
    }
}
