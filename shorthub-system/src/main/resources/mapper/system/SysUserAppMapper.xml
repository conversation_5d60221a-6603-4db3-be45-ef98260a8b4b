<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.SysUserAppMapper">

    <resultMap type="SysUserApp" id="SysUserAppResult">
        <result property="id"    column="id"    />
        <result property="userName"    column="user_name"    />
        <result property="appid"    column="appid"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- AppConfig resultMap for selectAppListByUserName method -->
    <resultMap type="tv.shorthub.common.core.domain.model.UserApp" id="UserAppResult">
        <result property="appid"    column="appid"    />
        <result property="appname"    column="appname"    />
        <result property="channel"    column="channel"    />
        <result property="extendJson"    column="extend_json"    />
    </resultMap>

    <sql id="selectSysUserAppVo">
        select id, user_name, appid, extend_json, create_by, create_time, update_by, update_time from sys_user_app
    </sql>

    <!-- 根据用户名查询用户授权的App配置列表 -->
    <select id="selectAppListByUserName" parameterType="String" resultMap="UserAppResult">
        SELECT sua.appid, ac.appname, ac.channel, ac.extend_json
        FROM app_config ac
        LEFT JOIN sys_user_app sua ON sua.appid = ac.appid
        <where>
            <if test="userName != null and userName != ''">
                sua.user_name = #{userName}
            </if>
        </where>
        ORDER BY ac.create_time DESC
    </select>

    <select id="getSummary" parameterType="SysUserApp" resultMap="SysUserAppResult">
        select
            max(id) as id
        from sys_user_app
        <where>
                    <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysUserAppResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="SysUserAppResult">
        
    </select>

</mapper>
