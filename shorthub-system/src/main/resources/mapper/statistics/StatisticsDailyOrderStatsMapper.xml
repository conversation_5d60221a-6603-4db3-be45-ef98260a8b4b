<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsDailyOrderStatsMapper">

    <resultMap type="StatisticsDailyOrderStats" id="StatisticsDailyOrderStatsResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="orderChannel"    column="order_channel"    />
        <result property="totalOrderCount"    column="total_order_count"    />
        <result property="paidOrderCount"    column="paid_order_count"    />
        <result property="paidUserCount"    column="paid_user_count"    />
        <result property="paidOrderAmount"    column="paid_order_amount"    />
        <result property="unpaidOrderCount"    column="unpaid_order_count"    />
        <result property="unpaidCoinOrderAmount"    column="unpaid_coin_order_amount"    />
        <result property="unpaidSubscriptionOrderAmount"    column="unpaid_subscription_order_amount"    />
        <result property="memberOrderCount"    column="member_order_count"    />
        <result property="memberOrderAmount"    column="member_order_amount"    />
        <result property="coinOrderCount"    column="coin_order_count"    />
        <result property="coinOrderAmount"    column="coin_order_amount"    />
        <result property="subscriptionOrderCount"    column="subscription_order_count"    />
        <result property="subscriptionOrderAmount"    column="subscription_order_amount"    />
        <result property="timezone"    column="timezone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsDailyOrderStatsVo">
        select id, stat_date, appid, tfid, order_channel, total_order_count, paid_order_count, paid_user_count, paid_order_amount, unpaid_order_count, unpaid_coin_order_amount, unpaid_subscription_order_amount, member_order_count, member_order_amount, coin_order_count, coin_order_amount, subscription_order_count, subscription_order_amount, timezone, create_by, create_time, update_by, update_time from statistics_daily_order_stats
    </sql>


    <select id="getSummary" parameterType="StatisticsDailyOrderStats" resultMap="StatisticsDailyOrderStatsResult">
        select
            max(id) as id
        from statistics_daily_order_stats
        <where>
                    <if test="statDate != null "> and stat_date = #{statDate}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="totalOrderCount != null "> and total_order_count = #{totalOrderCount}</if>
                    <if test="paidOrderCount != null "> and paid_order_count = #{paidOrderCount}</if>
                    <if test="paidUserCount != null "> and paid_user_count = #{paidUserCount}</if>
                    <if test="paidOrderAmount != null "> and paid_order_amount = #{paidOrderAmount}</if>
                    <if test="unpaidOrderCount != null "> and unpaid_order_count = #{unpaidOrderCount}</if>
                    <if test="unpaidCoinOrderAmount != null "> and unpaid_coin_order_amount = #{unpaidCoinOrderAmount}</if>
                    <if test="unpaidSubscriptionOrderAmount != null "> and unpaid_subscription_order_amount = #{unpaidSubscriptionOrderAmount}</if>
                    <if test="memberOrderCount != null "> and member_order_count = #{memberOrderCount}</if>
                    <if test="memberOrderAmount != null "> and member_order_amount = #{memberOrderAmount}</if>
                    <if test="coinOrderCount != null "> and coin_order_count = #{coinOrderCount}</if>
                    <if test="coinOrderAmount != null "> and coin_order_amount = #{coinOrderAmount}</if>
                    <if test="subscriptionOrderCount != null "> and subscription_order_count = #{subscriptionOrderCount}</if>
                    <if test="subscriptionOrderAmount != null "> and subscription_order_amount = #{subscriptionOrderAmount}</if>
                    <if test="timezone != null "> and timezone = #{timezone}</if>
                </where>
    </select>




    <!-- 查询指定日期范围内的每日订单统计数据 -->
    <select id="selectDailyOrderStats" resultMap="StatisticsDailyOrderStatsResult">
        WITH base_orders AS (
            SELECT
                -- 按日期分组，数据已经是UTC+8时区
                DATE_FORMAT(aoi.create_time, '%Y-%m-%d') AS stat_date,
                aoi.id,
                aoi.user_id,
                COALESCE(aoi.appid, 'default') as appid,
                COALESCE(aoi.tfid, '') as tfid,
                aoi.order_channel,
                aoi.order_status,
                aoi.order_amount,
                aoi.pay_type,
                aoi.create_time,
                aoi.pay_time,
                JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp')) AS client_ip
            FROM app_order_info aoi
            WHERE aoi.create_time BETWEEN #{startTime} AND #{endTime}
        ),
        successful_attempts AS (
            SELECT DISTINCT client_ip
            FROM base_orders
            WHERE order_status = 1 AND pay_time IS NOT NULL AND client_ip IS NOT NULL
        ),
        orders_for_aggregation AS (
            SELECT id, stat_date, appid, tfid, order_channel, user_id, order_status, order_amount, pay_type, create_time, pay_time, client_ip
            FROM base_orders
            WHERE order_status = 1 AND pay_time IS NOT NULL

            UNION ALL

            SELECT id, stat_date, appid, tfid, order_channel, user_id, order_status, order_amount, pay_type, create_time, pay_time, client_ip
            FROM (
                SELECT
                    b.*,
                    ROW_NUMBER() OVER(PARTITION BY b.client_ip, b.stat_date ORDER BY b.create_time ASC) as rn
                FROM
                    base_orders b
                LEFT JOIN
                    successful_attempts s ON b.client_ip = s.client_ip
                WHERE
                    b.order_status = 0 AND s.client_ip IS NULL
            ) AS truly_abandoned_unpaid_orders
            WHERE rn = 1
        )
        SELECT
            STR_TO_DATE(stat_date, '%Y-%m-%d') as stat_date,
            appid,
            COALESCE(tfid, '') as tfid,
            order_channel,
            -- 总订单数（按IP去重，CTE已处理去重逻辑）
            COUNT(DISTINCT client_ip) AS total_order_count,
            -- 支付成功订单数（不去重）
            COUNT(CASE WHEN order_status = 1 AND pay_time IS NOT NULL THEN 1 END) AS paid_order_count,
            -- 订单成功人数（按IP去重）
            COUNT(DISTINCT CASE WHEN order_status = 1 AND pay_time IS NOT NULL THEN client_ip END) AS paid_user_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL, order_amount, 0)) AS paid_order_amount,
            SUM(IF(order_status = 0, 1, 0)) AS unpaid_order_count,
            SUM(IF(order_status = 0 AND pay_type = '1', order_amount, 0)) AS unpaid_coin_order_amount,
            SUM(IF(order_status = 0 AND pay_type = '2', order_amount, 0)) AS unpaid_subscription_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '0', 1, 0)) AS member_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '0', order_amount, 0)) AS member_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '1', 1, 0)) AS coin_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '1', order_amount, 0)) AS coin_order_amount,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '2', 1, 0)) AS subscription_order_count,
            SUM(IF(order_status = 1 AND pay_time IS NOT NULL AND pay_type = '2', order_amount, 0)) AS subscription_order_amount,
            #{timezone} as timezone,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM orders_for_aggregation
        WHERE stat_date IS NOT NULL
        GROUP BY stat_date, appid, COALESCE(tfid, ''), order_channel
        ORDER BY stat_date, appid, COALESCE(tfid, ''), order_channel
    </select>

    <!-- 查询订单数据的最早和最晚日期 -->
    <select id="selectTimeRange" resultType="java.util.HashMap">
        SELECT MIN(create_time) AS min_date, MAX(create_time) AS max_date FROM app_order_info
    </select>

    <!-- 按多个tfid统计区间汇总 -->
    <select id="getSummaryByTfids" resultMap="StatisticsDailyOrderStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            SUM(total_order_count) AS total_order_count,
            SUM(paid_order_count) AS paid_order_count,
            SUM(paid_user_count) AS paid_user_count,
            SUM(paid_order_amount) AS paid_order_amount,
            SUM(unpaid_order_count) AS unpaid_order_count,
            SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
            SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
            SUM(member_order_count) AS member_order_count,
            SUM(member_order_amount) AS member_order_amount,
            SUM(coin_order_count) AS coin_order_count,
            SUM(coin_order_amount) AS coin_order_amount,
            SUM(subscription_order_count) AS subscription_order_count,
            SUM(subscription_order_amount) AS subscription_order_amount
        FROM statistics_daily_order_stats
        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        <if test="tfids != null and tfids.size() > 0">
            AND tfid IN
            <foreach collection="tfids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="orderChannel != null and orderChannel != ''">
            AND order_channel = #{orderChannel}
        </if>
    </select>

    <!-- 按创建者统计区间汇总 -->
    <select id="getSummaryByCreator" resultMap="StatisticsDailyOrderStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            NULL as order_channel,
            SUM(total_order_count) AS total_order_count,
            SUM(paid_order_count) AS paid_order_count,
            SUM(paid_user_count) AS paid_user_count,
            SUM(paid_order_amount) AS paid_order_amount,
            SUM(unpaid_order_count) AS unpaid_order_count,
            SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
            SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
            SUM(member_order_count) AS member_order_count,
            SUM(member_order_amount) AS member_order_amount,
            SUM(coin_order_count) AS coin_order_count,
            SUM(coin_order_amount) AS coin_order_amount,
            SUM(subscription_order_count) AS subscription_order_count,
            SUM(subscription_order_amount) AS subscription_order_amount
        FROM statistics_daily_order_stats s
        LEFT JOIN app_promotion p ON s.tfid = p.tfid
        WHERE s.stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
          AND s.timezone = #{timezone}
          AND (p.create_by = #{creator} OR s.tfid = '' OR s.tfid IS NULL)
        <if test="orderChannel != null and orderChannel != ''">
            AND s.order_channel = #{orderChannel}
        </if>
    </select>

    <!-- 按区间、appid、tfid统计汇总（管理员/通用） -->
    <select id="getRangeSummary" resultMap="StatisticsDailyOrderStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            SUM(total_order_count) AS total_order_count,
            SUM(paid_order_count) AS paid_order_count,
            SUM(paid_user_count) AS paid_user_count,
            SUM(paid_order_amount) AS paid_order_amount,
            SUM(unpaid_order_count) AS unpaid_order_count,
            SUM(unpaid_coin_order_amount) AS unpaid_coin_order_amount,
            SUM(unpaid_subscription_order_amount) AS unpaid_subscription_order_amount,
            SUM(member_order_count) AS member_order_count,
            SUM(member_order_amount) AS member_order_amount,
            SUM(coin_order_count) AS coin_order_count,
            SUM(coin_order_amount) AS coin_order_amount,
            SUM(subscription_order_count) AS subscription_order_count,
            SUM(subscription_order_amount) AS subscription_order_amount
        FROM statistics_daily_order_stats
        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND tfid = #{tfid}
        </if>
        <if test="orderChannel != null and orderChannel != ''">
            AND order_channel = #{orderChannel}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

</mapper>
