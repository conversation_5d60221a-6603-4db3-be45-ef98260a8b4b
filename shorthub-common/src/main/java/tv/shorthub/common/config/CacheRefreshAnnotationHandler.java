package tv.shorthub.common.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import lombok.extern.slf4j.Slf4j;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.refresh.CacheRefreshExecutor;
import tv.shorthub.common.core.cache.refresh.RefreshConfig;
import tv.shorthub.common.core.cache.refresh.RefreshService;

import java.util.Map;

@Component
@Slf4j
public class CacheRefreshAnnotationHandler implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    CacheRefreshExecutor cacheRefreshExecutor;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        ConfigurableListableBeanFactory beanFactory =
                ((ConfigurableApplicationContext) event.getApplicationContext()).getBeanFactory();

        for (String beanName : beanFactory.getBeanDefinitionNames()) {
            try {
                Object bean = beanFactory.getBean(beanName);
                Class<?> beanClass = bean.getClass();

                if (beanClass.isAnnotationPresent(CacheRefresh.class)) {
                    CacheRefresh annotation = beanClass.getAnnotation(CacheRefresh.class);
                    // 可以在这里触发缓存刷新逻辑
                    RefreshConfig refreshConfig = new RefreshConfig();
                    refreshConfig.setSort(annotation.sort());
                    refreshConfig.setKey(StringUtils.isEmpty(annotation.key()) ? beanName : annotation.key());
                    refreshConfig.setRefreshService((RefreshService) bean);
                    refreshConfig.setSeconds(annotation.seconds());
                    cacheRefreshExecutor.register(refreshConfig);
                    log.info("成功注册缓存刷新服务: {}", beanClass.getName());
                }
            } catch (BeansException e) {
                log.warn("无法获取Bean: {}", beanName, e);
            } catch (Exception e) {
                log.error("注册缓存刷新服务失败: {}", beanName, e);
            }
        }
        cacheRefreshExecutor.init();
    }
}
