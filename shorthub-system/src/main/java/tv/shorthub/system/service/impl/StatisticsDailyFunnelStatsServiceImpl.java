package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import tv.shorthub.system.mapper.StatisticsDailyFunnelStatsMapper;
import tv.shorthub.system.domain.StatisticsDailyFunnelStats;
import tv.shorthub.system.service.IStatisticsDailyFunnelStatsService;
import tv.shorthub.common.core.service.BaseService;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.utils.StatisticsPermissionUtils;

/**
 * 每日漏斗分析统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class StatisticsDailyFunnelStatsServiceImpl extends BaseService<StatisticsDailyFunnelStats> implements IStatisticsDailyFunnelStatsService
{
    @Autowired
    private StatisticsDailyFunnelStatsMapper statisticsDailyFunnelStatsMapper;

    @Autowired
    private StatisticsPermissionUtils permissionUtils;

    @Override
    public StatisticsDailyFunnelStatsMapper getMapper() {
        return statisticsDailyFunnelStatsMapper;
    }

    /**
     * 查询每日漏斗分析统计
     *
     * @param id 每日漏斗分析统计主键
     * @return 每日漏斗分析统计
     */
    @Override
    public StatisticsDailyFunnelStats getById(Long id)
    {
        return statisticsDailyFunnelStatsMapper.selectById(id);
    }

    /**
     * 查询每日漏斗分析统计列表
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计
     */
    @Override
    public List<StatisticsDailyFunnelStats> selectList(StatisticsDailyFunnelStats query)
    {
        QueryWrapper<StatisticsDailyFunnelStats> wrapper = new QueryWrapper<>(query);
        // 按时间倒序排列，最新数据在前
        wrapper.orderByDesc("stat_date");
        // 权限过滤
        permissionUtils.filterByDeliverPermission(wrapper);

        return statisticsDailyFunnelStatsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询每日漏斗分析统计数据汇总
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计
     */
    @Override
    public StatisticsDailyFunnelStats getSummary(StatisticsDailyFunnelStats query)
    {
        return statisticsDailyFunnelStatsMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计
     */
    @Override
    public List<StatisticsDailyFunnelStats> summary(SummaryRequest query)
    {
        return statisticsDailyFunnelStatsMapper.summary(query);
    }

    @Override
    public StatisticsDailyFunnelStats allSummary(SummaryRequest query)
    {
        return statisticsDailyFunnelStatsMapper.allSummary(query);
    }

    /**
     * 新增每日漏斗分析统计
     *
     * @param statisticsDailyFunnelStats 每日漏斗分析统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        statisticsDailyFunnelStats.setCreateTime(DateUtils.getNowDate());
        return statisticsDailyFunnelStatsMapper.insert(statisticsDailyFunnelStats);
    }

    /**
     * 修改每日漏斗分析统计
     *
     * @param statisticsDailyFunnelStats 每日漏斗分析统计
     * @return 结果
     */
    @Override
    public int update(StatisticsDailyFunnelStats statisticsDailyFunnelStats)
    {
        statisticsDailyFunnelStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsDailyFunnelStatsMapper.updateById(statisticsDailyFunnelStats);
    }

    /**
     * 批量删除每日漏斗分析统计
     *
     * @param ids 需要删除的每日漏斗分析统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsDailyFunnelStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除每日漏斗分析统计信息
     *
     * @param id 每日漏斗分析统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsDailyFunnelStatsMapper.deleteById(id);
    }

    /**
     * 根据时间范围获取每日漏斗统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param timezone 时区
     * @return 汇总统计数据
     */
    @Override
    public StatisticsDailyFunnelStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone)
    {
        // 主账号强制限定 appid
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailyFunnelStatsMapper.getRangeSummary(startTime, endTime, appid, tfid, timezone);
    }

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param timezone 时区
     * @return 汇总统计数据
     */
    @Override
    public StatisticsDailyFunnelStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String timezone)
    {
        // 如果是业务主账号（但不是系统管理员），强制使用自己的 appid
        String appid = null;
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin()) {
            appid = SecurityUtils.getAppid();
        }
        return statisticsDailyFunnelStatsMapper.getSummaryByTfids(tfids, startTime, endTime, timezone, appid);
    }

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param timezone 时区
     * @return 汇总统计数据
     */
    @Override
    public StatisticsDailyFunnelStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String timezone)
    {
        // 主账号自己看自己appid下所有数据（含自然流量）
        if (SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin() &&
            (org.apache.commons.lang3.StringUtils.isEmpty(creatorName) || SecurityUtils.getUsername().equals(creatorName))) {
            if (startTime != null && endTime != null) {
                return statisticsDailyFunnelStatsMapper.getRangeSummary(startTime, endTime, SecurityUtils.getAppid(), null, timezone);
            } else {
                StatisticsDailyFunnelStats query = new StatisticsDailyFunnelStats();
                query.setAppid(SecurityUtils.getAppid());
                return statisticsDailyFunnelStatsMapper.getSummary(query);
            }
        }
        // 其他情况：系统管理员或主账号看指定子账号
        List<String> tfids = permissionUtils.getUserTfids(creatorName);
        if (tfids.isEmpty()) {
            return new StatisticsDailyFunnelStats();
        }
        return getSummaryByTfids(tfids, startTime, endTime, timezone);
    }
}
