<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppLoginRecordsMapper">

    <resultMap type="AppLoginRecords" id="AppLoginRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginTime"    column="login_time"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceInfo"    column="device_info"    />
        <result property="browserInfo"    column="browser_info"    />
        <result property="osInfo"    column="os_info"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="locationCountry"    column="location_country"    />
        <result property="locationRegion"    column="location_region"    />
        <result property="locationCity"    column="location_city"    />
        <result property="loginMethod"    column="login_method"    />
        <result property="success"    column="success"    />
        <result property="failureReason"    column="failure_reason"    />
        <result property="sessionId"    column="session_id"    />
        <result property="twoFactorUsed"    column="two_factor_used"    />
        <result property="loginDuration"    column="login_duration"    />
        <result property="isNewDevice"    column="is_new_device"    />
        <result property="isNewLocation"    column="is_new_location"    />
        <result property="extendJson"    column="extend_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppLoginRecordsVo">
        select id, user_id, login_time, ip_address, device_type, device_info, browser_info, os_info, user_agent, location_country, location_region, location_city, login_method, success, failure_reason, session_id, two_factor_used, login_duration, is_new_device, is_new_location, extend_json, create_by, create_time, update_by, update_time, remark from app_login_records
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_login_records
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.userId != null and item.userId != ''">user_id,</if>
                        <if test="item.loginTime != null">login_time,</if>
                        <if test="item.ipAddress != null">ip_address,</if>
                        <if test="item.deviceType != null">device_type,</if>
                        <if test="item.deviceInfo != null">device_info,</if>
                        <if test="item.browserInfo != null">browser_info,</if>
                        <if test="item.osInfo != null">os_info,</if>
                        <if test="item.userAgent != null">user_agent,</if>
                        <if test="item.locationCountry != null">location_country,</if>
                        <if test="item.locationRegion != null">location_region,</if>
                        <if test="item.locationCity != null">location_city,</if>
                        <if test="item.loginMethod != null">login_method,</if>
                        <if test="item.success != null">success,</if>
                        <if test="item.failureReason != null">failure_reason,</if>
                        <if test="item.sessionId != null">session_id,</if>
                        <if test="item.twoFactorUsed != null">two_factor_used,</if>
                        <if test="item.loginDuration != null">login_duration,</if>
                        <if test="item.isNewDevice != null">is_new_device,</if>
                        <if test="item.isNewLocation != null">is_new_location,</if>
                        <if test="item.extendJson != null">extend_json,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
                        <if test="item.remark != null">remark,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.userId != null and item.userId != ''">#{item.userId},</if>
                        <if test="item.loginTime != null">#{item.loginTime},</if>
                        <if test="item.ipAddress != null">#{item.ipAddress},</if>
                        <if test="item.deviceType != null">#{item.deviceType},</if>
                        <if test="item.deviceInfo != null">#{item.deviceInfo},</if>
                        <if test="item.browserInfo != null">#{item.browserInfo},</if>
                        <if test="item.osInfo != null">#{item.osInfo},</if>
                        <if test="item.userAgent != null">#{item.userAgent},</if>
                        <if test="item.locationCountry != null">#{item.locationCountry},</if>
                        <if test="item.locationRegion != null">#{item.locationRegion},</if>
                        <if test="item.locationCity != null">#{item.locationCity},</if>
                        <if test="item.loginMethod != null">#{item.loginMethod},</if>
                        <if test="item.success != null">#{item.success},</if>
                        <if test="item.failureReason != null">#{item.failureReason},</if>
                        <if test="item.sessionId != null">#{item.sessionId},</if>
                        <if test="item.twoFactorUsed != null">#{item.twoFactorUsed},</if>
                        <if test="item.loginDuration != null">#{item.loginDuration},</if>
                        <if test="item.isNewDevice != null">#{item.isNewDevice},</if>
                        <if test="item.isNewLocation != null">#{item.isNewLocation},</if>
                        <if test="item.extendJson != null">#{item.extendJson},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
                        <if test="item.remark != null">#{item.remark},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.userId != null and item.userId != ''">user_id = values(user_id),</if>
                        <if test="item.loginTime != null">login_time = values(login_time),</if>
                        <if test="item.ipAddress != null">ip_address = values(ip_address),</if>
                        <if test="item.deviceType != null">device_type = values(device_type),</if>
                        <if test="item.deviceInfo != null">device_info = values(device_info),</if>
                        <if test="item.browserInfo != null">browser_info = values(browser_info),</if>
                        <if test="item.osInfo != null">os_info = values(os_info),</if>
                        <if test="item.userAgent != null">user_agent = values(user_agent),</if>
                        <if test="item.locationCountry != null">location_country = values(location_country),</if>
                        <if test="item.locationRegion != null">location_region = values(location_region),</if>
                        <if test="item.locationCity != null">location_city = values(location_city),</if>
                        <if test="item.loginMethod != null">login_method = values(login_method),</if>
                        <if test="item.success != null">success = values(success),</if>
                        <if test="item.failureReason != null">failure_reason = values(failure_reason),</if>
                        <if test="item.sessionId != null">session_id = values(session_id),</if>
                        <if test="item.twoFactorUsed != null">two_factor_used = values(two_factor_used),</if>
                        <if test="item.loginDuration != null">login_duration = values(login_duration),</if>
                        <if test="item.isNewDevice != null">is_new_device = values(is_new_device),</if>
                        <if test="item.isNewLocation != null">is_new_location = values(is_new_location),</if>
                        <if test="item.extendJson != null">extend_json = values(extend_json),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
                        <if test="item.remark != null">remark = values(remark),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppLoginRecords" resultMap="AppLoginRecordsResult">
        select
            max(id) as id
        from app_login_records
        <where>
                    <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                    <if test="loginTime != null "> and login_time = #{loginTime}</if>
                    <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
                    <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
                    <if test="deviceInfo != null  and deviceInfo != ''"> and device_info = #{deviceInfo}</if>
                    <if test="browserInfo != null  and browserInfo != ''"> and browser_info = #{browserInfo}</if>
                    <if test="osInfo != null  and osInfo != ''"> and os_info = #{osInfo}</if>
                    <if test="userAgent != null  and userAgent != ''"> and user_agent = #{userAgent}</if>
                    <if test="locationCountry != null  and locationCountry != ''"> and location_country = #{locationCountry}</if>
                    <if test="locationRegion != null  and locationRegion != ''"> and location_region = #{locationRegion}</if>
                    <if test="locationCity != null  and locationCity != ''"> and location_city = #{locationCity}</if>
                    <if test="loginMethod != null  and loginMethod != ''"> and login_method = #{loginMethod}</if>
                    <if test="success != null "> and success = #{success}</if>
                    <if test="failureReason != null  and failureReason != ''"> and failure_reason = #{failureReason}</if>
                    <if test="sessionId != null  and sessionId != ''"> and session_id = #{sessionId}</if>
                    <if test="twoFactorUsed != null "> and two_factor_used = #{twoFactorUsed}</if>
                    <if test="loginDuration != null "> and login_duration = #{loginDuration}</if>
                    <if test="isNewDevice != null "> and is_new_device = #{isNewDevice}</if>
                    <if test="isNewLocation != null "> and is_new_location = #{isNewLocation}</if>
                    <if test="extendJson != null "> and extend_json = #{extendJson}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppLoginRecordsResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppLoginRecordsResult">
        
    </select>

</mapper>
