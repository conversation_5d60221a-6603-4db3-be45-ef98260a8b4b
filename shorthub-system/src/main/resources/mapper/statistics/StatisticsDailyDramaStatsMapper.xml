<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsDailyDramaStatsMapper">

    <resultMap type="StatisticsDailyDramaStats" id="StatisticsDailyDramaStatsResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="appid"    column="appid"    />
        <result property="contentId"    column="content_id"    />
        <result property="tfid"    column="tfid"    />
        <result property="vvCount"    column="vv_count"    />
        <result property="uvCount"    column="uv_count"    />
        <result property="totalWatchSeconds"    column="total_watch_seconds"    />
        <result property="paidUnlockCount"    column="paid_unlock_count"    />
        <result property="memberUnlockCount"    column="member_unlock_count"    />
        <result property="totalCoinSpent"    column="total_coin_spent"    />
        <result property="timezone"    column="timezone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsDailyDramaStatsVo">
        select id, stat_date, appid, content_id, tfid, vv_count, uv_count, total_watch_seconds, paid_unlock_count, member_unlock_count, total_coin_spent, timezone, create_by, create_time, update_by, update_time from statistics_daily_drama_stats
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into statistics_daily_drama_stats
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.statDate != null">stat_date,</if>
                <if test="item.appid != null and item.appid != ''">appid,</if>
                <if test="item.contentId != null and item.contentId != ''">content_id,</if>
                <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                <if test="item.vvCount != null">vv_count,</if>
                <if test="item.uvCount != null">uv_count,</if>
                <if test="item.totalWatchSeconds != null">total_watch_seconds,</if>
                <if test="item.paidUnlockCount != null">paid_unlock_count,</if>
                <if test="item.memberUnlockCount != null">member_unlock_count,</if>
                <if test="item.totalCoinSpent != null">total_coin_spent,</if>
                <if test="item.timezone != null">timezone,</if>
                <if test="item.createBy != null">create_by,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.updateBy != null">update_by,</if>
                <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.statDate != null">#{item.statDate},</if>
                <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                <if test="item.contentId != null and item.contentId != ''">#{item.contentId},</if>
                <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                <if test="item.vvCount != null">#{item.vvCount},</if>
                <if test="item.uvCount != null">#{item.uvCount},</if>
                <if test="item.totalWatchSeconds != null">#{item.totalWatchSeconds},</if>
                <if test="item.paidUnlockCount != null">#{item.paidUnlockCount},</if>
                <if test="item.memberUnlockCount != null">#{item.memberUnlockCount},</if>
                <if test="item.totalCoinSpent != null">#{item.totalCoinSpent},</if>
                <if test="item.timezone != null">#{item.timezone},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                <if test="item.statDate != null">stat_date = values(stat_date),</if>
                <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                <if test="item.contentId != null and item.contentId != ''">content_id = values(content_id),</if>
                <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                <if test="item.vvCount != null">vv_count = values(vv_count),</if>
                <if test="item.uvCount != null">uv_count = values(uv_count),</if>
                <if test="item.totalWatchSeconds != null">total_watch_seconds = values(total_watch_seconds),</if>
                <if test="item.paidUnlockCount != null">paid_unlock_count = values(paid_unlock_count),</if>
                <if test="item.memberUnlockCount != null">member_unlock_count = values(member_unlock_count),</if>
                <if test="item.totalCoinSpent != null">total_coin_spent = values(total_coin_spent),</if>
                <if test="item.timezone != null">timezone = values(timezone),</if>
                <if test="item.createBy != null">create_by = values(create_by),</if>
                <if test="item.createTime != null">create_time = values(create_time),</if>
                <if test="item.updateBy != null">update_by = values(update_by),</if>
                <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="StatisticsDailyDramaStats" resultMap="StatisticsDailyDramaStatsResult">
        select
        max(id) as id
        from statistics_daily_drama_stats
        <where>
            <if test="statDate != null "> and stat_date = #{statDate}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
            <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
            <if test="vvCount != null "> and vv_count = #{vvCount}</if>
            <if test="uvCount != null "> and uv_count = #{uvCount}</if>
            <if test="totalWatchSeconds != null "> and total_watch_seconds = #{totalWatchSeconds}</if>
            <if test="paidUnlockCount != null "> and paid_unlock_count = #{paidUnlockCount}</if>
            <if test="memberUnlockCount != null "> and member_unlock_count = #{memberUnlockCount}</if>
            <if test="totalCoinSpent != null "> and total_coin_spent = #{totalCoinSpent}</if>
            <if test="timezone != null  and timezone != ''"> and timezone = #{timezone}</if>
        </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsDailyDramaStatsResult">
        SELECT
        SUM(vv_count) as vv_count,
        SUM(uv_count) as uv_count,
        SUM(total_watch_seconds) as total_watch_seconds,
        SUM(paid_unlock_count) as paid_unlock_count,
        SUM(member_unlock_count) as member_unlock_count,
        SUM(total_coin_spent) as total_coin_spent
        FROM statistics_daily_drama_stats
        WHERE stat_date >= #{startTime} AND stat_date &lt;= #{endTime}
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND tfid = #{tfid}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="StatisticsDailyDramaStatsResult">
        SELECT
        SUM(vv_count) as vv_count,
        SUM(uv_count) as uv_count,
        SUM(total_watch_seconds) as total_watch_seconds,
        SUM(paid_unlock_count) as paid_unlock_count,
        SUM(member_unlock_count) as member_unlock_count,
        SUM(total_coin_spent) as total_coin_spent
        FROM statistics_daily_drama_stats
        WHERE stat_date >= #{startTime} AND stat_date &lt;= #{endTime}
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <!-- 根据时间范围查询每日剧集统计数据 -->
    <select id="selectDailyDramaStats" resultMap="StatisticsDailyDramaStatsResult">
        WITH base_watch_data AS (
            -- 基础观看数据 - 数据库存储的是UTC+8时间，直接按日期分组
            SELECT
            DATE(w.watched_at) as stat_date,
            COALESCE(w.appid, 'default') as appid,
            w.content_id,
            COALESCE(w.tfid, '') as tfid,
            w.user_id,
            w.serial_number,
        -- 每用户每天每内容只取最大观看时长，避免重复计算
            MAX(CASE WHEN w.max_watch_second > 0 THEN w.max_watch_second ELSE 60 END) as watch_seconds,
        -- 付费集数逻辑：有tfid用推广链接设置，没有tfid用内容默认设置
            MAX(CASE
            WHEN w.tfid IS NOT NULL AND w.tfid != '' AND ap.video_fee_begin IS NOT NULL
            THEN ap.video_fee_begin
            ELSE COALESCE(dc.fee_begin, 1)
            END) as fee_begin
        FROM app_user_watch_records w
            INNER JOIN app_drama_contents dc ON w.content_id = dc.content_id
            LEFT JOIN app_promotion ap ON w.tfid = ap.tfid AND w.tfid IS NOT NULL AND w.tfid != ''
        WHERE DATE(w.watched_at) >= #{startTime}
          AND DATE(w.watched_at) &lt;= #{endTime}
          AND w.content_id IS NOT NULL
          AND COALESCE(w.appid, 'default') IS NOT NULL
        -- 关键：按用户、日期、内容分组去重
        GROUP BY DATE(w.watched_at), COALESCE(w.appid, 'default'), w.content_id,
            COALESCE(w.tfid, ''), w.user_id, w.serial_number
            ),
            watch_stats AS (
        SELECT
            stat_date,
            appid,
            content_id,
            tfid,
            COUNT(*) as vv_count,
            COUNT(DISTINCT user_id) as uv_count,
            SUM(watch_seconds) as total_watch_seconds
        FROM base_watch_data
        GROUP BY stat_date, appid, content_id, tfid
            ),
            consumption_stats AS (
        -- 金币消费统计
        SELECT
            DATE(c.create_time) as stat_date,
            COALESCE(c.appid, 'default') as appid,
            COALESCE(first_order.tfid, '') as tfid,
            c.content_id,
            COUNT(DISTINCT c.user_id) as paid_unlock_count,
            SUM(ABS(c.amount)) as total_coin_spent
        FROM app_consumption c
            LEFT JOIN (
            -- 获取每个用户的首次成功充值推广链接
            SELECT
            user_id,
            tfid,
            ROW_NUMBER() OVER(PARTITION BY user_id ORDER BY create_time ASC) as rn
            FROM app_order_info
            WHERE order_status = 1 AND pay_time IS NOT NULL
            ) first_order ON c.user_id = first_order.user_id AND first_order.rn = 1
        WHERE c.amount &lt; 0
          AND DATE(c.create_time) >= #{startTime}
          AND DATE(c.create_time) &lt;= #{endTime}
          AND c.content_id IS NOT NULL
        GROUP BY
            DATE(c.create_time),
            COALESCE(c.appid, 'default'),
            COALESCE(first_order.tfid, ''),
            c.content_id
            ),
            subscribers AS (
        -- 订阅用户
        SELECT DISTINCT
            user_id,
            COALESCE(appid, 'default') as appid
        FROM app_order_info
        WHERE order_status = 1 AND pay_type = '2' AND pay_time IS NOT NULL
            ),
            member_unlock_stats AS (
        -- 会员解锁统计
        SELECT
            w.stat_date,
            w.appid,
            w.content_id,
            COUNT(DISTINCT w.user_id) as member_unlock_count
        FROM base_watch_data w
            INNER JOIN subscribers s ON w.user_id = s.user_id AND w.appid = s.appid
        WHERE w.serial_number >= w.fee_begin
        GROUP BY w.stat_date, w.appid, w.content_id
            )
        SELECT
            ws.stat_date,
            ws.appid,
            ws.content_id,
            ws.tfid,
            ws.vv_count,
            ws.uv_count,
            ws.total_watch_seconds,
            COALESCE(cs.paid_unlock_count, 0) as paid_unlock_count,
            COALESCE(ms.member_unlock_count, 0) as member_unlock_count,
            COALESCE(cs.total_coin_spent, 0) as total_coin_spent,
            #{timezone} as timezone,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM watch_stats ws
                 LEFT JOIN consumption_stats cs ON ws.stat_date = cs.stat_date
            AND ws.appid = cs.appid
            AND ws.tfid = cs.tfid
            AND ws.content_id = cs.content_id
                 LEFT JOIN member_unlock_stats ms ON ws.stat_date = ms.stat_date
            AND ws.appid = ms.appid
            AND ws.content_id = ms.content_id
        ORDER BY ws.stat_date, ws.appid, ws.content_id
    </select>

    <!-- 根据时间范围获取剧集统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsDailyDramaStatsResult">
        SELECT
        SUM(vv_count) as vv_count,
        SUM(uv_count) as uv_count,
        SUM(total_watch_seconds) as total_watch_seconds,
        SUM(paid_unlock_count) as paid_unlock_count,
        SUM(member_unlock_count) as member_unlock_count,
        SUM(total_coin_spent) as total_coin_spent
        FROM statistics_daily_drama_stats
        WHERE stat_date >= #{startTime} AND stat_date &lt;= #{endTime}
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND tfid = #{tfid}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <!-- 根据时间范围查询剧集统计列表 -->
    <select id="selectListByTimeRange" resultMap="StatisticsDailyDramaStatsResult">
        <include refid="selectStatisticsDailyDramaStatsVo"/>
        WHERE stat_date >= #{startTime} AND stat_date &lt;= #{endTime}
        <if test="query.appid != null and query.appid != ''">
            AND appid = #{query.appid}
        </if>
        <if test="query.tfid != null and query.tfid != ''">
            AND tfid = #{query.tfid}
        </if>
        <if test="query.contentId != null and query.contentId != ''">
            AND content_id = #{query.contentId}
        </if>
        <if test="query.timezone != null and query.timezone != ''">
            AND timezone = #{query.timezone}
        </if>
        ORDER BY stat_date DESC
    </select>

    <!-- 根据 tfid 列表获取汇总数据 -->
    <select id="getSummaryByTfids" resultMap="StatisticsDailyDramaStatsResult">
        SELECT
        SUM(vv_count) as vv_count,
        SUM(uv_count) as uv_count,
        SUM(total_watch_seconds) as total_watch_seconds,
        SUM(paid_unlock_count) as paid_unlock_count,
        SUM(member_unlock_count) as member_unlock_count,
        SUM(total_coin_spent) as total_coin_spent
        FROM statistics_daily_drama_stats
        WHERE tfid IN
        <foreach collection="tfids" item="tfid" open="(" separator="," close=")">
            #{tfid}
        </foreach>
        <if test="startTime != null">
            AND stat_date >= #{startTime}
        </if>
        <if test="endTime != null">
            AND stat_date &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <!-- 根据创建者获取汇总数据 -->
    <select id="getSummaryByCreator" resultMap="StatisticsDailyDramaStatsResult">
        SELECT
        SUM(vv_count) as vv_count,
        SUM(uv_count) as uv_count,
        SUM(total_watch_seconds) as total_watch_seconds,
        SUM(paid_unlock_count) as paid_unlock_count,
        SUM(member_unlock_count) as member_unlock_count,
        SUM(total_coin_spent) as total_coin_spent
        FROM statistics_daily_drama_stats
        INNER JOIN app_promotion ap ON statistics_daily_drama_stats.tfid = ap.tfid
        WHERE ap.create_by = #{creatorName}
        <if test="startDate != null">
            AND stat_date >= #{startTime}
        </if>
        <if test="endDate != null">
            AND stat_date &lt;= #{endTime}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

</mapper>
