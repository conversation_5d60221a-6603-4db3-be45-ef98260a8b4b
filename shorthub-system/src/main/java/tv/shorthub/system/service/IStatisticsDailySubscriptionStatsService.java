package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsDailySubscriptionStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.StatisticsDailySubscriptionStats;
import tv.shorthub.common.core.service.IBaseService;
import java.util.Date;

/**
 * 每日订阅统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IStatisticsDailySubscriptionStatsService extends IBaseService<StatisticsDailySubscriptionStats>
{
    /**
     * 查询每日订阅统计
     *
     * @param id 每日订阅统计主键
     * @return 每日订阅统计
     */
    public StatisticsDailySubscriptionStats getById(Long id);

    /**
     * 查询每日订阅统计数据汇总
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计数据汇总
     */
    public StatisticsDailySubscriptionStats getSummary(StatisticsDailySubscriptionStats query);

    /**
     * 查询每日订阅统计列表
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计集合
     */
    public List<StatisticsDailySubscriptionStats> selectList(StatisticsDailySubscriptionStats query);

    /**
     * 新增每日订阅统计
     *
     * @param statisticsDailySubscriptionStats 每日订阅统计
     * @return 结果
     */
    public int insert(StatisticsDailySubscriptionStats statisticsDailySubscriptionStats);

    /**
     * 修改每日订阅统计
     *
     * @param statisticsDailySubscriptionStats 每日订阅统计
     * @return 结果
     */
    public int update(StatisticsDailySubscriptionStats statisticsDailySubscriptionStats);

    /**
     * 批量删除每日订阅统计
     *
     * @param ids 需要删除的每日订阅统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每日订阅统计信息
     *
     * @param id 每日订阅统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每日订阅统计
     * @return 每日订阅统计集合
     */
    public List<StatisticsDailySubscriptionStats> summary(SummaryRequest query);

    StatisticsDailySubscriptionStats allSummary(SummaryRequest query);

    StatisticsDailySubscriptionStatsMapper getMapper();

    /**
     * 按多个tfid统计区间汇总
     */
    StatisticsDailySubscriptionStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime);

    /**
     * 按多个tfid统计区间汇总（支持时区）
     */
    StatisticsDailySubscriptionStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String timezone);

    /**
     * 按创建者统计区间汇总
     */
    StatisticsDailySubscriptionStats getSummaryByCreator(String creator, Date startTime, Date endTime);

    /**
     * 按创建者统计区间汇总（支持时区）
     */
    StatisticsDailySubscriptionStats getSummaryByCreator(String creator, Date startTime, Date endTime, String timezone);

    /**
     * 按区间、appid、tfid统计汇总
     */
    StatisticsDailySubscriptionStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone);

    /**
     * 查询指定日期范围内的每日订阅统计数据（支持时区）
     */
    List<StatisticsDailySubscriptionStats> selectDailySubscriptionStatsWithTimezone(Date startTime, Date endTime, String timezone);
}
