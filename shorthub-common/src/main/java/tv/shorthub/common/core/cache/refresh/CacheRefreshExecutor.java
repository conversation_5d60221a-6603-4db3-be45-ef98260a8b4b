package tv.shorthub.common.core.cache.refresh;

import jakarta.annotation.PreDestroy;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import tv.shorthub.common.core.redis.RedisCache;
import tv.shorthub.common.enums.SysEnv;
import tv.shorthub.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Slf4j
public class CacheRefreshExecutor  implements ApplicationListener<ApplicationStartedEvent> {

    private static Map<String, RefreshConfig> cacheConfigMap = new HashMap<>();

    private final Map<String, ScheduledTask> scheduledTasks = new HashMap<>();
    
    // 用于跟踪正在执行的任务
    private final Map<String, AtomicBoolean> runningTasks = new ConcurrentHashMap<>();

    private static final ScheduledExecutorService VIRTUAL_THREAD_POOL =
            Executors.newScheduledThreadPool(0, Thread.ofVirtual().factory());

    @Autowired
    RedisCache redisCache;

    private boolean refreshedEventInit;

    public static RefreshConfig getConfig(String tableName) {
        return cacheConfigMap.get(tableName);
    }


    public void init() {
        if (refreshedEventInit) {
            return;
        }
        refreshedEventInit = true;
        refresh(true);
    }

    public void register(RefreshConfig refreshConfig) {
        if (refreshConfig.getSort() == 0) {
            refreshConfig.setSort(cacheConfigMap.size());
        }
        cacheConfigMap.put(refreshConfig.getKey(), refreshConfig);
        log.info("注册缓存配置: {}", refreshConfig.getKey());


        // 如果设置了 seconds，则启动定时任务
        if (refreshConfig.getSeconds() > 0) {
            scheduleRefreshTask(refreshConfig.getKey(), refreshConfig.getSeconds());
        }
    }

    private void scheduleRefreshTask(String key, int intervalSeconds) {
        ScheduledTask task = scheduledTasks.get(key);

        if (task != null) {
            // 如果已有任务并且间隔不同，则取消旧任务并重新创建
            if (task.intervalSeconds == intervalSeconds) {
                return; // 不需要重复注册相同间隔的任务
            } else {
                task.future.cancel(false); // 取消旧任务
            }
        }

        // 提交新任务
        ScheduledFuture<?> future = VIRTUAL_THREAD_POOL.scheduleAtFixedRate(
                () -> refresh(key),
                intervalSeconds,
                intervalSeconds,
                TimeUnit.SECONDS
        );

        scheduledTasks.put(key, new ScheduledTask(future, intervalSeconds));
        log.info("已为 {} 启动定时刷新任务，间隔 {} 秒", key, intervalSeconds);
    }

    @PreDestroy
    public void destroy() {
        for (ScheduledTask task : scheduledTasks.values()) {
            task.future.cancel(true);
        }
        scheduledTasks.clear();
        runningTasks.clear();
        VIRTUAL_THREAD_POOL.shutdownNow();
    }



    public void refresh(Boolean init) {
        log.info("###### 刷新缓存 ######");
        long startTime = System.currentTimeMillis();
        cacheConfigMap.values().stream()
                .sorted(Comparator.comparing(RefreshConfig::getSort))
                .forEach(refreshConfig -> {
                        refresh(refreshConfig.getKey());
                    }
        );
        log.info("###### ALL 缓存刷新完成 {}ms. ######", System.currentTimeMillis() - startTime);
    }

    public void refresh(String key) {
        if (!cacheConfigMap.containsKey(key)) {
            log.info("未注册缓存刷新服务: {}", key);
            return;
        }

        // 获取或创建任务的运行状态标记
        AtomicBoolean isRunning = runningTasks.computeIfAbsent(key, k -> new AtomicBoolean(false));
        
        // 如果任务正在运行，则跳过本次执行
        if (!isRunning.compareAndSet(false, true)) {
            log.info("任务 {} 正在执行中，跳过本次执行", key);
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            cacheConfigMap.get(key).getRefreshService().start();
            log.info("###### 缓存刷新完成 {}ms. ######", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("缓存刷新失败: key={}, error={}", key, e.getMessage(), e);
        } finally {
            // 确保任务执行完成后重置状态
            isRunning.set(false);
        }
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
//        init();
    }

    public static void refreshByKey(String key) {
        SpringUtils.getBean(CacheRefreshExecutor.class).refresh(key);
    }


    private static class ScheduledTask {
        private volatile ScheduledFuture<?> future;
        private int intervalSeconds;

        public ScheduledTask(ScheduledFuture<?> future, int intervalSeconds) {
            this.future = future;
            this.intervalSeconds = intervalSeconds;
        }
    }

}
