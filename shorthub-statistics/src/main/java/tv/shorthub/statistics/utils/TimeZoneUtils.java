package tv.shorthub.statistics.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 时区转换工具类
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public class TimeZoneUtils {
    
    /**
     * 系统默认时区（UTC+8）
     */
    private static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("GMT+8");
    
    /**
     * 将UTC格式的时区字符串转换为时区偏移量（小时）
     * 例如："UTC+8" -> 8, "UTC-8" -> -8
     * 
     * @param timezoneStr UTC格式的时区字符串
     * @return 时区偏移量（小时）
     */
    public static int getTimezoneOffset(String timezoneStr) {
        if (timezoneStr == null || timezoneStr.isEmpty()) {
            return 8; // 默认UTC+8
        }
        
        try {
            // 处理 UTC+8, UTC-8 等格式
            if (timezoneStr.startsWith("UTC")) {
                String offsetStr = timezoneStr.substring(3);
                return Integer.parseInt(offsetStr);
            }
            return 8; // 默认UTC+8
        } catch (Exception e) {
            return 8; // 解析失败返回默认值
        }
    }
    
    /**
     * 根据目标时区调整时间范围
     * 将系统时间（UTC+8）转换为目标时区的时间
     * 
     * @param date 原始时间（UTC+8）
     * @param targetTimezone 目标时区（如 "UTC-8"）
     * @return 调整后的时间
     */
    public static Date convertToTargetTimezone(Date date, String targetTimezone) {
        if (date == null) {
            return null;
        }
        
        // 获取目标时区偏移量
        int targetOffset = getTimezoneOffset(targetTimezone);
        
        // 系统默认时区偏移量（UTC+8）
        int systemOffset = 8;
        
        // 计算时差（小时）
        int offsetDiff = targetOffset - systemOffset;
        
        // 调整时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, offsetDiff);
        
        return calendar.getTime();
    }
    
    /**
     * 根据目标时区调整查询的时间范围
     * 将目标时区的"一天"转换为对应的系统时间（UTC+8）范围用于查询
     *
     * @param startTime 开始时间（系统时间，表示目标时区的某一天开始）
     * @param endTime 结束时间（系统时间，表示目标时区的某一天结束）
     * @param targetTimezone 目标时区（如 "UTC-8"）
     * @return 调整后的时间范围数组 [startTime, endTime]（UTC+8系统时间）
     */
    public static Date[] adjustTimeRangeForQuery(Date startTime, Date endTime, String targetTimezone) {
        if (startTime == null || endTime == null) {
            return new Date[]{startTime, endTime};
        }

        // 如果是系统默认时区，直接返回
        if ("UTC+8".equals(targetTimezone)) {
            return new Date[]{startTime, endTime};
        }

        // 获取目标时区偏移量
        int targetOffset = getTimezoneOffset(targetTimezone);

        // 系统默认时区偏移量（UTC+8）
        int systemOffset = 8;

        // 计算时差（小时）
        // 目标：将目标时区的"一天"映射到系统时间
        // 例如：UTC-8时区的2025-01-01 00:00-24:00 对应 UTC+8的2025-01-01 16:00到2025-01-02 16:00
        // 所以需要将系统时间向后偏移 (系统偏移量 - 目标偏移量) 小时
        int offsetDiff = systemOffset - targetOffset;

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startTime);
        startCal.add(Calendar.HOUR_OF_DAY, offsetDiff);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endTime);
        endCal.add(Calendar.HOUR_OF_DAY, offsetDiff);

        return new Date[]{startCal.getTime(), endCal.getTime()};
    }
    
    /**
     * 获取时区的GMT格式字符串
     * 例如："UTC+8" -> "GMT+08:00"
     * 
     * @param timezoneStr UTC格式的时区字符串
     * @return GMT格式的时区字符串
     */
    public static String getGMTTimezoneString(String timezoneStr) {
        int offset = getTimezoneOffset(timezoneStr);
        String sign = offset >= 0 ? "+" : "-";
        int absOffset = Math.abs(offset);
        return String.format("GMT%s%02d:00", sign, absOffset);
    }
}
