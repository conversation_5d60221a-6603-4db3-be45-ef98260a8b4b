现在负责的是shorthub-statistics 模块 还有 statistics 相关的代码

tv.shorthub.system.utils.StatisticsPermissionUtils：统一处理统计模块的权限验证和数据过滤逻辑

tv.shorthub.admin.utils.StatisticsControllerPermissionUtils：统一处理统计Controller的权限验证和数据过滤逻辑

权限 管理员 > 主账号 > 子账号

管理员可以查看所有appid 的数据 主账号可以查看 当前appid 的数据 而子账号 投手维度 只可以查看他所创建的推广链接 tfid 的数据

技术 Java + MyBatis-Plus + MySQL + redis

管理模块  shorthub-system/domain shorthub-system/mapper  service service/impl resources/statistics 有关的statistics admin/controller/statistics

public interface CommonMapper<T> extends BaseMapper<T> {

&nbsp;    T getSummary(T T);

&nbsp;    List<T> summary(SummaryRequest T);

&nbsp;    T allSummary(SummaryRequest T);

&nbsp;    int batchInsertOrUpdate(List<T> list);

}
这些有专门的拦截器可以xml 不需要填写
核心表 推广链接表 app_promotion
记住：没有.sql 脚本
-- 权限控制条件
-- ADMIN: 所有 appid 数据
-- 主账号: 指定 appid 下所有数据
-- 子账号: 指定 appid 下自己投放的 tfid 数据
```
