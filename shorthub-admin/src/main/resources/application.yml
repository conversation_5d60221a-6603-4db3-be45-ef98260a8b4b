# 项目相关配置
shorthub:
  # 名称
  name: shorthub短剧平台
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # API节点域名
  domainApi: https://market.shorthub.tv
  # web客户端域名
  domainWeb: https://www.shorthub.tv

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    tv.shorthub: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: local
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 2048MB
      file-size-threshold: 1MB
      # 设置总上传的文件大小
      max-request-size: 2048MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 2880

## MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: tv.shorthub.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  typeAliasesPackage: tv.shorthub.**.domain
  type-handlers-package: tv.shorthub
  global-config:
    db-config:
      id-type: input
    enable-sql-runner: true

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-apicorpus

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 钉钉配置
dingtalk:
  # 是否启用钉钉服务
  enabled: true
  # 企业内部应用配置
  app:
    # 应用的AppKey
    app-key: ${DINGTALK_APP_KEY:your_app_key}
    # 应用的AppSecret
    app-secret: ${DINGTALK_APP_SECRET:your_app_secret}
    # 企业ID
    corp-id: ${DINGTALK_CORP_ID:your_corp_id}
    # 应用的AgentId
    agent-id: ${DINGTALK_AGENT_ID:*********}
  # 群机器人配置
  robot:
    # 机器人的Webhook地址
    webhook: ${DINGTALK_WEBHOOK:https://oapi.dingtalk.com/robot/send?access_token=your_access_token}
    # 机器人的加签密钥（如果启用了加签）
    secret: ${DINGTALK_SECRET:your_secret_key}
    # 是否启用加签
    enable-sign: true

google:
  play:
    # 应用配置列表
    apps:
      - packageName: "com.b.shorthub.tv"
        serviceAccountKeyPath: "ace-world-464010-i4-64900a8cf96b.json"
        appName: ShortHub TV
        enabled: true
        defaultLanguage: zh-HK
        defaultCurrency: HKD
        defaultRegionCode: HK
    # 多语言配置
    language:
      # 语言模板映射
      configs:
        en-US:
          # 产品类型模板
          member: Premium Membership
          member_desc: Unlock all premium features and enjoy ad-free experience
          coin: Coin Package
          coin_desc: Get coins to unlock premium content and features
          subscription: Premium Subscription
          subscription_desc: Monthly premium subscription with exclusive benefits
          # 默认模板
          inapp_title: Product
          inapp_description: In-app product
          subscription_title: Subscription
          subscription_description: Subscription service
          currency: USD
          region_code: US
        zh-TW:
          # 产品类型模板
          member: 會員服務
          member_desc: 解鎖所有高級功能，享受無廣告體驗
          coin: 金幣套餐
          coin_desc: 獲得金幣解鎖高級內容和功能
          subscription: 會員訂閱
          subscription_desc: 月度會員訂閱，享受專屬權益
          # 默认模板
          inapp_title: 產品
          inapp_description: 應用內產品
          subscription_title: 訂閱
          subscription_description: 訂閱服務
          currency: TWD
          region_code: TW

        zh-HK:
          # 产品类型模板
          member: 會員服務
          member_desc: 解鎖所有高級功能，享受無廣告體驗
          coin: 金幣套餐
          coin_desc: 獲得金幣解鎖高級內容和功能
          subscription: 會員訂閱
          subscription_desc: 月度會員訂閱，享受專屬權益
          # 默认模板
          inapp_title: 產品
          inapp_description: 應用內產品
          subscription_title: 訂閱
          subscription_description: 訂閱服務
          currency: HKD
          region_code: HK

# 统计模块配置
statistics:
  # 支持的时区列表（UTC格式，24个标准时区）
  supported-timezones:
    - "UTC-12"  # 国际日期变更线西
    - "UTC-11"  # 萨摩亚时间
    - "UTC-10"  # 夏威夷时间
    - "UTC-9"   # 阿拉斯加时间
    - "UTC-8"   # 美西时间
    - "UTC-7"   # 美山时间
    - "UTC-6"   # 美中时间
    - "UTC-5"   # 美东时间
    - "UTC-4"   # 大西洋时间
    - "UTC-3"   # 巴西/阿根廷时间
    - "UTC-2"   # 大西洋中部时间
    - "UTC-1"   # 亚速尔时间
    - "UTC+0"   # 英国/UTC时间
    - "UTC+1"   # 中欧时间
    - "UTC+2"   # 东欧时间
    - "UTC+3"   # 莫斯科时间
    - "UTC+4"   # 阿联酋时间
    - "UTC+5"   # 巴基斯坦时间
    - "UTC+6"   # 孟加拉国时间
    - "UTC+7"   # 东南亚时间
    - "UTC+8"   # 中国标准时间
    - "UTC+9"   # 日本/韩国时间
    - "UTC+10"  # 澳洲东部时间
    - "UTC+11"  # 所罗门群岛时间
    - "UTC+12"  # 新西兰时间
