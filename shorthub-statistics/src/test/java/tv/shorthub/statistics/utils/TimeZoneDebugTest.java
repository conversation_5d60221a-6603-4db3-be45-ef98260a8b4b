package tv.shorthub.statistics.utils;

import org.junit.Test;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时区转换调试测试
 */
public class TimeZoneDebugTest {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testCurrentLogic() {
        System.out.println("=== 当前时区转换逻辑测试 ===");
        
        // 模拟定时任务：统计 2025-05-01 这一天的数据
        Calendar cal = Calendar.getInstance();
        cal.set(2025, Calendar.MAY, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime(); // 2025-05-01 00:00:00
        
        cal.add(Calendar.DATE, 1);
        Date endTime = cal.getTime(); // 2025-05-02 00:00:00
        
        System.out.println("原始时间范围（系统时间 UTC+8）:");
        System.out.println("  开始: " + sdf.format(startTime));
        System.out.println("  结束: " + sdf.format(endTime));
        
        // 测试不同时区的转换
        String[] timezones = {"UTC+8", "UTC-8", "UTC+0"};
        
        for (String timezone : timezones) {
            Date[] adjusted = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
            System.out.println("\n" + timezone + " 时区调整后的查询时间范围:");
            System.out.println("  开始: " + sdf.format(adjusted[0]));
            System.out.println("  结束: " + sdf.format(adjusted[1]));
            
            // 分析这个时间范围的含义
            analyzeTimeRange(timezone, startTime, endTime, adjusted[0], adjusted[1]);
        }
    }
    
    private void analyzeTimeRange(String timezone, Date originalStart, Date originalEnd, 
                                 Date adjustedStart, Date adjustedEnd) {
        System.out.println("  分析：");
        
        if (timezone.equals("UTC+8")) {
            System.out.println("    UTC+8时区的2025-05-01应该查询系统时间的2025-05-01 00:00到24:00");
            System.out.println("    当前查询范围是否正确：" + 
                (sdf.format(adjustedStart).equals("2025-05-01 00:00:00") && 
                 sdf.format(adjustedEnd).equals("2025-05-02 00:00:00") ? "✓" : "✗"));
        } else if (timezone.equals("UTC-8")) {
            System.out.println("    UTC-8时区的2025-05-01 00:00对应UTC+8的2025-05-01 16:00");
            System.out.println("    UTC-8时区的2025-05-02 00:00对应UTC+8的2025-05-02 16:00");
            System.out.println("    所以应该查询系统时间的2025-05-01 16:00到2025-05-02 16:00");
            System.out.println("    当前查询范围是否正确：" + 
                (sdf.format(adjustedStart).equals("2025-05-01 16:00:00") && 
                 sdf.format(adjustedEnd).equals("2025-05-02 16:00:00") ? "✓" : "✗"));
        }
    }
    
    @Test
    public void testExpectedBehavior() {
        System.out.println("\n=== 期望的业务逻辑 ===");
        System.out.println("定时任务要为每个时区生成该时区'当天'的统计数据");
        System.out.println("比如统计2025-05-01这一天：");
        System.out.println("- UTC+8时区：查询系统时间2025-05-01 00:00到24:00的订单");
        System.out.println("- UTC-8时区：查询UTC-8时间2025-05-01 00:00到24:00对应的系统时间的订单");
        System.out.println("  即：UTC+8时间2025-05-01 16:00到2025-05-02 16:00的订单");
    }
}
