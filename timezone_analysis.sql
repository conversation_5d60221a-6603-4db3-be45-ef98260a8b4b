-- 修复后的 SQL 语句
SELECT 
    timezone,
    stat_date,
    COUNT(*) as record_count,
    SUM(paid_order_amount) as total_amount
FROM statistics_daily_order_stats 
WHERE timezone IN ('UTC+8', 'UTC-8') 
AND stat_date IN ('2025-08-04', '2025-08-05')
GROUP BY timezone, stat_date
ORDER BY timezone, stat_date;

-- 查看更多边界日期的对比
SELECT 
    timezone,
    stat_date,
    COUNT(*) as record_count,
    SUM(paid_order_amount) as total_amount
FROM statistics_daily_order_stats 
WHERE timezone IN ('UTC+8', 'UTC-8') 
AND stat_date BETWEEN '2025-08-01' AND '2025-08-05'
GROUP BY timezone, stat_date
ORDER BY stat_date, timezone;

-- 查看每个时区的总体统计
SELECT 
    timezone,
    COUNT(*) as total_records,
    SUM(paid_order_amount) as total_amount,
    MIN(stat_date) as min_date,
    MAX(stat_date) as max_date
FROM statistics_daily_order_stats 
WHERE timezone IN ('UTC+8', 'UTC-8')
GROUP BY timezone
ORDER BY timezone;

-- 查看数据差异最大的几天
SELECT 
    stat_date,
    SUM(CASE WHEN timezone = 'UTC+8' THEN paid_order_amount ELSE 0 END) as utc8_amount,
    SUM(CASE WHEN timezone = 'UTC-8' THEN paid_order_amount ELSE 0 END) as utc_minus8_amount,
    SUM(CASE WHEN timezone = 'UTC+8' THEN paid_order_amount ELSE 0 END) - 
    SUM(CASE WHEN timezone = 'UTC-8' THEN paid_order_amount ELSE 0 END) as difference
FROM statistics_daily_order_stats 
WHERE timezone IN ('UTC+8', 'UTC-8')
GROUP BY stat_date
HAVING ABS(difference) > 100
ORDER BY ABS(difference) DESC
LIMIT 10;
