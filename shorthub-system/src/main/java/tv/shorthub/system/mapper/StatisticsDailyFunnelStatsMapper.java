package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsDailyFunnelStats;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;

/**
 * 每日漏斗分析统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface StatisticsDailyFunnelStatsMapper extends CommonMapper<StatisticsDailyFunnelStats>
{
    /**
     * 查询指定日期范围内的每日漏斗分析统计数据（支持时区）
     */
    List<StatisticsDailyFunnelStats> selectStatisticsByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone);

    /**
     * 批量插入或更新统计数据
     */
    int batchInsertOrUpdate(List<StatisticsDailyFunnelStats> list);

    /**
     * 批量插入或更新统计数据 V1版本（直接SQL实现）
     */
    int batchInsertOrUpdateV1(List<StatisticsDailyFunnelStats> list);

    /**
     * 查询漏斗分析数据的最早和最晚日期
     */
    @MapKey("min_date")
    Map<String, Date> selectTimeRange();

    /**
     * 按多个tfid统计区间汇总
     */
    StatisticsDailyFunnelStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone, @Param("appid") String appid);

    /**
     * 按创建者统计区间汇总
     */
    StatisticsDailyFunnelStats getSummaryByCreator(@Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone);

    /**
     * 按区间、appid、tfid统计汇总
     */
    StatisticsDailyFunnelStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid, @Param("timezone") String timezone);
}
