package tv.shorthub.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 区间汇总查询请求参数
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class RangeSummaryRequest {
    
    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    /** 应用ID */
    private String appid;
    
    /** 推广链接ID */
    private String tfid;
    
    /** 内容ID */
    private String contentId;
    
    /** 剧目ID */
    private String dramaId;
    
    /** 投手名称 */
    private String delivererName;
}