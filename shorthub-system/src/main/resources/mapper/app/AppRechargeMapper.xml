<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.AppRechargeMapper">

    <resultMap type="AppRecharge" id="AppRechargeResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="templateId"    column="template_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="remake"    column="remake"    />
        <result property="sort"    column="sort"    />
        <result property="enabled"    column="enabled"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppRechargeVo">
        select id, appid, template_id, template_name, remake, sort, enabled, is_default, create_by, create_time, update_by, update_time from app_recharge
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into app_recharge
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">appid,</if>
                        <if test="item.templateId != null">template_id,</if>
                        <if test="item.templateName != null">template_name,</if>
                        <if test="item.remake != null">remake,</if>
                        <if test="item.sort != null">sort,</if>
                        <if test="item.enabled != null">enabled,</if>
                        <if test="item.isDefault != null">is_default,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.appid != null">#{item.appid},</if>
                        <if test="item.templateId != null">#{item.templateId},</if>
                        <if test="item.templateName != null">#{item.templateName},</if>
                        <if test="item.remake != null">#{item.remake},</if>
                        <if test="item.sort != null">#{item.sort},</if>
                        <if test="item.enabled != null">#{item.enabled},</if>
                        <if test="item.isDefault != null">#{item.isDefault},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.appid != null">appid = values(appid),</if>
                        <if test="item.templateId != null">template_id = values(template_id),</if>
                        <if test="item.templateName != null">template_name = values(template_name),</if>
                        <if test="item.remake != null">remake = values(remake),</if>
                        <if test="item.sort != null">sort = values(sort),</if>
                        <if test="item.enabled != null">enabled = values(enabled),</if>
                        <if test="item.isDefault != null">is_default = values(is_default),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="AppRecharge" resultMap="AppRechargeResult">
        select
            max(id) as id
        from app_recharge
        <where>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
                    <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
                    <if test="remake != null  and remake != ''"> and remake = #{remake}</if>
                    <if test="sort != null "> and sort = #{sort}</if>
                    <if test="enabled != null "> and enabled = #{enabled}</if>
                    <if test="isDefault != null "> and is_default = #{isDefault}</if>
                </where>
    </select>



    <select id="summary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeResult">
        
    </select>
    <select id="allSummary" parameterType="tv.shorthub.common.core.domain.SummaryRequest" resultMap="AppRechargeResult">
        
    </select>

</mapper>
