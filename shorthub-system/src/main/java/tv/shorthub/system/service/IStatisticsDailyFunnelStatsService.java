package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsDailyFunnelStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.system.domain.StatisticsDailyFunnelStats;
import tv.shorthub.common.core.service.IBaseService;
import org.apache.ibatis.annotations.Param;

/**
 * 每日漏斗分析统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IStatisticsDailyFunnelStatsService extends IBaseService<StatisticsDailyFunnelStats>
{
    /**
     * 查询每日漏斗分析统计
     *
     * @param id 每日漏斗分析统计主键
     * @return 每日漏斗分析统计
     */
    public StatisticsDailyFunnelStats getById(Long id);

    /**
     * 查询每日漏斗分析统计数据汇总
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计数据汇总
     */
    public StatisticsDailyFunnelStats getSummary(StatisticsDailyFunnelStats query);

    /**
     * 查询每日漏斗分析统计列表
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计集合
     */
    public List<StatisticsDailyFunnelStats> selectList(StatisticsDailyFunnelStats query);

    /**
     * 新增每日漏斗分析统计
     *
     * @param statisticsDailyFunnelStats 每日漏斗分析统计
     * @return 结果
     */
    public int insert(StatisticsDailyFunnelStats statisticsDailyFunnelStats);

    /**
     * 修改每日漏斗分析统计
     *
     * @param statisticsDailyFunnelStats 每日漏斗分析统计
     * @return 结果
     */
    public int update(StatisticsDailyFunnelStats statisticsDailyFunnelStats);

    /**
     * 批量删除每日漏斗分析统计
     *
     * @param ids 需要删除的每日漏斗分析统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每日漏斗分析统计信息
     *
     * @param id 每日漏斗分析统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 每日漏斗分析统计
     * @return 每日漏斗分析统计集合
     */
    public List<StatisticsDailyFunnelStats> summary(SummaryRequest query);

    StatisticsDailyFunnelStats allSummary(SummaryRequest query);

    StatisticsDailyFunnelStatsMapper getMapper();

    /**
     * 根据时间范围获取每日漏斗统计汇总数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param timezone 时区
     * @return 汇总统计数据
     */
    StatisticsDailyFunnelStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone);

    /**
     * 根据 tfid 列表获取汇总数据
     *
     * @param tfids tfid 列表
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param timezone 时区
     * @return 汇总统计数据
     */
    StatisticsDailyFunnelStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String timezone);

    /**
     * 根据创建者获取其所有子账号的汇总数据
     *
     * @param creatorName 创建者用户名
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param timezone 时区
     * @return 汇总统计数据
     */
    StatisticsDailyFunnelStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String timezone);
}
