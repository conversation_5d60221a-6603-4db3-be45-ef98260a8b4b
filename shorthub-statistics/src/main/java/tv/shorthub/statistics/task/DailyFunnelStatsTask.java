 package tv.shorthub.statistics.task;

 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Component;
 import tv.shorthub.statistics.service.IDailyFunnelStatsService;

 /**
  * 每日漏斗分析统计定时任务
  *
  * <AUTHOR>
  * @date 2025-07-08
  */
 @Slf4j
 @Component
 public class DailyFunnelStatsTask {

     @Autowired
     private IDailyFunnelStatsService dailyFunnelStatsService;

     /**
      * 每1分钟执行一次，更新当天的漏斗分析统计数据
      * cron表达式: 0 0/1 * * * ? 表示每1分钟执行一次
      */
     @Scheduled(cron = "0 0/1 * * * ?")
     public void updateCurrentDayFunnelStats() {
         log.info("[漏斗分析日统计定时任务] 开始执行每日漏斗分析统计任务");
         try {
             dailyFunnelStatsService.updateCurrentDayFunnelStats();
             log.info("[漏斗分析日统计定时任务] 每日漏斗分析统计任务执行完成");
         } catch (Exception e) {
             log.error("[漏斗分析日统计定时任务] 执行失败: {}", e.getMessage(), e);
         }
     }

     /**
      * 每日凌晨2:15执行，全量统计昨天一整天的数据，作为数据兜底
      * cron表达式: 0 15 2 * * ?
      */
     @Scheduled(cron = "0 15 2 * * ?")
     public void batchUpdateYesterdayStats() {
         log.info("[漏斗分析日统计每日兜底任务] 开始执行");
         try {
             dailyFunnelStatsService.batchUpdateRecentFunnelStats(1);
             log.info("[漏斗分析日统计每日兜底任务] 成功完成");
         } catch (Exception e) {
             log.error("[漏斗分析日统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
         }
     }

     /**
      * 每周日凌晨3:15执行，全量重算所有历史数据
      * cron表达式: 0 15 3 ? * SUN
      */
     @Scheduled(cron = "0 15 3 ? * SUN")
     public void batchUpdateAllStats() {
         log.info("[漏斗分析日统计每周全量任务] 开始执行");
         try {
             String result = dailyFunnelStatsService.batchUpdateAllStats();
             log.info("[漏斗分析日统计每周全量任务] 执行完成: {}", result);
         } catch (Exception e) {
             log.error("[漏斗分析日统计每周全量任务] 执行失败: {}", e.getMessage(), e);
         }
     }
 }
