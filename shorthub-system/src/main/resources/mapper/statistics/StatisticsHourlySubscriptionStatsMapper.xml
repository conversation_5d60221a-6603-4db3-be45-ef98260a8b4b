<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsHourlySubscriptionStatsMapper">

    <resultMap type="StatisticsHourlySubscriptionStats" id="StatisticsHourlySubscriptionStatsResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="statHour"    column="stat_hour"    />
        <result property="newSubscriptionOrderCount"    column="new_subscription_order_count"    />
        <result property="newSubscriptionOrderAmount"    column="new_subscription_order_amount"    />
        <result property="successfulRenewalCount"    column="successful_renewal_count"    />
        <result property="successfulRenewalAmount"    column="successful_renewal_amount"    />
        <result property="totalOrderCount"    column="total_order_count"    />
        <result property="totalOrderAmount"    column="total_order_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="tv.shorthub.system.dto.SubscriptionSummaryDto" id="SubscriptionSummaryDtoResult" extends="StatisticsHourlySubscriptionStatsResult">
        <result property="upcomingSevenDayAmount" column="upcoming_seven_day_amount" />
        <result property="upcomingThirtyDayAmount" column="upcoming_thirty_day_amount" />
    </resultMap>

    <sql id="selectStatisticsHourlySubscriptionStatsVo">
        select id, appid, tfid, stat_hour, new_subscription_order_count, new_subscription_order_amount, successful_renewal_count, successful_renewal_amount, total_order_count, total_order_amount, create_by, create_time, update_by, update_time from statistics_hourly_subscription_stats
    </sql>

    <insert id="batchInsertOrUpdateStats">
        INSERT INTO statistics_hourly_subscription_stats
        (appid, tfid, stat_hour, new_subscription_order_count, new_subscription_order_amount,
        successful_renewal_count, successful_renewal_amount, total_order_count, total_order_amount,
        create_by, create_time, update_by, update_time)
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (<choose>
            <when test="item.appid != null and item.appid != ''">#{item.appid}</when>
            <otherwise>'default'</otherwise>
        </choose>,
            COALESCE(#{item.tfid}, ''),
            #{item.statHour},
            #{item.newSubscriptionOrderCount},
            #{item.newSubscriptionOrderAmount},
            #{item.successfulRenewalCount},
            #{item.successfulRenewalAmount},
            #{item.totalOrderCount},
            #{item.totalOrderAmount},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        new_subscription_order_count = VALUES(new_subscription_order_count),
        new_subscription_order_amount = VALUES(new_subscription_order_amount),
        successful_renewal_count = VALUES(successful_renewal_count),
        successful_renewal_amount = VALUES(successful_renewal_amount),
        total_order_count = VALUES(total_order_count),
        total_order_amount = VALUES(total_order_amount),
        update_by = VALUES(update_by),
        update_time = VALUES(update_time)
    </insert>

    <select id="getSummary" parameterType="StatisticsHourlySubscriptionStats" resultMap="StatisticsHourlySubscriptionStatsResult">
        select
        max(id) as id
        from statistics_hourly_subscription_stats
        <where>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="statHour != null "> and stat_hour = #{statHour}</if>
            <if test="newSubscriptionOrderCount != null "> and new_subscription_order_count = #{newSubscriptionOrderCount}</if>
            <if test="newSubscriptionOrderAmount != null "> and new_subscription_order_amount = #{newSubscriptionOrderAmount}</if>
            <if test="successfulRenewalCount != null "> and successful_renewal_count = #{successfulRenewalCount}</if>
            <if test="successfulRenewalAmount != null "> and successful_renewal_amount = #{successfulRenewalAmount}</if>
            <if test="totalOrderCount != null "> and total_order_count = #{totalOrderCount}</if>
            <if test="totalOrderAmount != null "> and total_order_amount = #{totalOrderAmount}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
    </select>




    <!-- 根据时间范围查询每小时订阅统计数据（支持tfid分组） -->
    <select id="selectHourlySubscriptionStats" resultMap="StatisticsHourlySubscriptionStatsResult">
        WITH order_subscriptions AS (
            -- 订单表中的首次订阅数据，按IP去重统计
            SELECT
                DATE_FORMAT(aoi.pay_time, '%Y-%m-%d %H:00:00') AS stat_hour,
                COALESCE(aoi.appid, 'default') AS appid,
                COALESCE(aoi.tfid, '') as tfid,
                -- 按IP去重统计首次订阅人数
                COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp'))) AS new_subscription_count,
                SUM(aoi.order_amount) AS new_subscription_amount
            FROM app_order_info aoi
            WHERE aoi.pay_time BETWEEN #{startTime} AND #{endTime}
              AND aoi.order_status = 1
              AND aoi.pay_type = '2'
              AND JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp')) IS NOT NULL
            GROUP BY stat_hour, appid, tfid
        ),
        paypal_subscriptions AS (
            -- PayPal订阅续订数据 (paypal_subscription_renewal表) - 只统计续订，不统计首次
            SELECT
                DATE_FORMAT(psr.renewal_time, '%Y-%m-%d %H:00:00') AS stat_hour,
                COALESCE(aoi.appid, 'default') AS appid,
                COALESCE(aoi.tfid, '') as tfid,  -- 将NULL转换为空字符串，保持一致性
                -- 首次订阅改为从订单表统计，这里只统计续订
                0 AS new_subscription_count,
                0 AS new_subscription_amount,
                COUNT(CASE WHEN psr.cycle_number > 1 THEN psr.id END) AS renewal_count,
                SUM(CASE WHEN psr.cycle_number > 1 THEN psr.amount ELSE 0 END) AS renewal_amount
            FROM paypal_subscription_renewal psr
                     JOIN app_order_info aoi ON psr.order_no = aoi.order_no
            WHERE psr.renewal_time BETWEEN #{startTime} AND #{endTime}
              AND psr.status = 'ACTIVE'
            GROUP BY stat_hour, appid, tfid
        ),
             plan_subscriptions AS (
                 -- PayPal签约数据 (paypal_user_plan_record表)
                 -- 修正：签约表中所有记录都是续订，period_num=1就是第一次续订
                 SELECT
                     DATE_FORMAT(pupr.create_time, '%Y-%m-%d %H:00:00') AS stat_hour,
                     COALESCE(aoi.appid, 'default') AS appid,
                     COALESCE(aoi.tfid, '') as tfid,  -- 将NULL转换为空字符串，保持一致性
                     -- 签约表中没有首次订阅，所有记录都是续订
                     0 AS plan_new_subscription_count,
                     0 AS plan_new_subscription_amount,
                     -- 签约表中所有记录都是续订（period_num >= 1）
                     COUNT(pupr.id) AS plan_renewal_count,
                     SUM(pupr.amount) AS plan_renewal_amount
                 FROM paypal_user_plan_record pupr
                          JOIN app_order_info aoi ON pupr.order_no = aoi.order_no
                 WHERE pupr.create_time BETWEEN #{startTime} AND #{endTime}
                   AND pupr.update_by != 'true'
                 GROUP BY stat_hour, appid, tfid
             ),
             airwallex_plan_subscriptions AS (
                 -- Airwallex签约数据 (airwallex_user_plan_record表)
                 -- 跟PayPal签约逻辑一样：period_num > 1 就是续订
                 SELECT
                     DATE_FORMAT(aupr.create_time, '%Y-%m-%d %H:00:00') AS stat_hour,
                     COALESCE(aoi.appid, 'default') AS appid,
                     COALESCE(aoi.tfid, '') as tfid,  -- 将NULL转换为空字符串，保持一致性
                     -- 签约表中没有首次订阅，所有记录都是续订
                     0 AS airwallex_new_subscription_count,
                     0 AS airwallex_new_subscription_amount,
                     -- 签约表中所有成功记录都是续订
                     COUNT(CASE WHEN aupr.success = 1 THEN aupr.id END) AS airwallex_renewal_count,
                     SUM(CASE WHEN aupr.success = 1 THEN aupr.amount ELSE 0 END) AS airwallex_renewal_amount
                 FROM airwallex_user_plan_record aupr
                          JOIN app_order_info aoi ON aupr.order_no = aoi.order_no
                 WHERE aupr.create_time BETWEEN #{startTime} AND #{endTime}
                 GROUP BY stat_hour, appid, tfid
             ),
             active_hours_grid AS (
                 -- 构建活跃时段、AppID和tfid的基础网格
                 SELECT stat_hour, appid, tfid FROM order_subscriptions
                 UNION ALL
                 SELECT stat_hour, appid, tfid FROM paypal_subscriptions
                 UNION ALL
                 SELECT stat_hour, appid, tfid FROM plan_subscriptions
                 UNION ALL
                 SELECT stat_hour, appid, tfid FROM airwallex_plan_subscriptions
             )
        -- 最终查询：按tfid分组统计订阅数据
        SELECT
            STR_TO_DATE(ag.stat_hour, '%Y-%m-%d %H:%i:%s') as stat_hour,
            ag.appid,
            COALESCE(ag.tfid, '') as tfid,  -- 将NULL转换为空字符串，避免唯一索引问题
            -- 首次订阅：来自订单表的订阅类型订单，按IP去重
            COALESCE(os.new_subscription_count, 0) AS new_subscription_order_count,
            COALESCE(os.new_subscription_amount, 0.00) AS new_subscription_order_amount,
            -- 续订：paypal_subscription_renewal表的cycle_number>=2 + paypal_user_plan_record表的所有记录 + airwallex_user_plan_record表的period_num>1记录
            (COALESCE(ps.renewal_count, 0) + COALESCE(pls.plan_renewal_count, 0) + COALESCE(aps.airwallex_renewal_count, 0)) AS successful_renewal_count,
            (COALESCE(ps.renewal_amount, 0.00) + COALESCE(pls.plan_renewal_amount, 0.00) + COALESCE(aps.airwallex_renewal_amount, 0.00)) AS successful_renewal_amount,
            -- 总数：首次订阅 + 续订
            (COALESCE(os.new_subscription_count, 0) + COALESCE(ps.renewal_count, 0) + COALESCE(pls.plan_renewal_count, 0) + COALESCE(aps.airwallex_renewal_count, 0)) AS total_order_count,
            (COALESCE(os.new_subscription_amount, 0.00) + COALESCE(ps.renewal_amount, 0.00) + COALESCE(pls.plan_renewal_amount, 0.00) + COALESCE(aps.airwallex_renewal_amount, 0.00)) AS total_order_amount,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM active_hours_grid ag
                 LEFT JOIN order_subscriptions os ON ag.stat_hour = os.stat_hour AND ag.appid = os.appid AND COALESCE(ag.tfid, '') = COALESCE(os.tfid, '')
                 LEFT JOIN paypal_subscriptions ps ON ag.stat_hour = ps.stat_hour AND ag.appid = ps.appid AND COALESCE(ag.tfid, '') = COALESCE(ps.tfid, '')
                 LEFT JOIN plan_subscriptions pls ON ag.stat_hour = pls.stat_hour AND ag.appid = pls.appid AND COALESCE(ag.tfid, '') = COALESCE(pls.tfid, '')
                 LEFT JOIN airwallex_plan_subscriptions aps ON ag.stat_hour = aps.stat_hour AND ag.appid = aps.appid AND COALESCE(ag.tfid, '') = COALESCE(aps.tfid, '')
        ORDER BY ag.stat_hour, ag.appid, ag.tfid
    </select>

    <!-- 根据时间范围获取订阅统计汇总数据 -->
    <select id="getRangeSummary" resultMap="StatisticsHourlySubscriptionStatsResult">
        SELECT
        NULL AS stat_hour,
        <choose>
            <when test="appid != null and appid != ''">#{appid}</when>
            <otherwise>'all'</otherwise>
        </choose> AS appid,
        NULL AS tfid,
        COALESCE(SUM(s.new_subscription_order_count), 0) AS new_subscription_order_count,
        COALESCE(SUM(s.new_subscription_order_amount), 0) AS new_subscription_order_amount,
        COALESCE(SUM(s.successful_renewal_count), 0) AS successful_renewal_count,
        COALESCE(SUM(s.successful_renewal_amount), 0) AS successful_renewal_amount,
        COALESCE(SUM(s.total_order_count), 0) AS total_order_count,
        COALESCE(SUM(s.total_order_amount), 0) AS total_order_amount,
        NULL as create_by,
        NOW() AS create_time,
        NULL as update_by,
        NOW() AS update_time
        FROM statistics_hourly_subscription_stats s
        WHERE s.stat_hour BETWEEN #{startTime} AND #{endTime}
        <if test="appid != null and appid != ''">
            AND s.appid = #{appid}
        </if>
        <if test="tfid != null">
            <choose>
                <when test="tfid == ''">AND (s.tfid IS NULL or s.tfid = '')</when>
                <otherwise>AND s.tfid = #{tfid}</otherwise>
            </choose>
        </if>
    </select>

    <!-- 根据 tfid 列表获取汇总数据（投手权限：返回完整数据，前端控制显示） -->
    <select id="getSummaryByTfids" resultMap="StatisticsHourlySubscriptionStatsResult">
        SELECT
        NULL as id,
        NULL as stat_hour,
        NULL as appid,
        NULL as tfid,
        -- 返回完整的订阅数据，让前端根据权限控制显示
        SUM(new_subscription_order_count) AS new_subscription_order_count,
        SUM(new_subscription_order_amount) AS new_subscription_order_amount,
        -- 续订数据也返回，前端根据hideRenewalData决定是否显示
        SUM(successful_renewal_count) AS successful_renewal_count,
        SUM(successful_renewal_amount) AS successful_renewal_amount,
        -- 总数包含首次订阅和续订
        SUM(total_order_count) AS total_order_count,
        SUM(total_order_amount) AS total_order_amount,
        NULL as create_by,
        NOW() as create_time,
        NULL as update_by,
        NOW() as update_time
        FROM statistics_hourly_subscription_stats
        WHERE tfid IN
        <foreach item="tfid" collection="tfids" open="(" separator="," close=")">
            #{tfid}
        </foreach>
        <if test="startTime != null">
            AND stat_hour BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
    </select>

    <select id="selectUpcomingPayments" resultMap="SubscriptionSummaryDtoResult">
        WITH active_subscriptions AS (
            -- 从 renewal 表获取有效的、有下一次扣款时间的订阅
            SELECT
                r.subscription_id,
                r.amount,
                r.next_billing_time
            FROM
                paypal_subscription_renewal r
            INNER JOIN (
                -- 找到每个订阅ID最新的那条记录
                SELECT subscription_id, MAX(cycle_number) AS max_cycle
                FROM paypal_subscription_renewal
                GROUP BY subscription_id
            ) latest_r ON r.subscription_id = latest_r.subscription_id AND r.cycle_number = latest_r.max_cycle
            WHERE
                r.status = 'ACTIVE' AND r.next_billing_time IS NOT NULL

            UNION ALL

            -- 从 plan_record 表获取有效的、需要计算下次扣款时间的订阅
            SELECT
                p.payment_id AS subscription_id, -- 使用 payment_id 作为唯一标识
                p.amount,
                -- 计算下一次扣款时间
                CASE
                    WHEN p.period = 'DAY' THEN DATE_ADD(latest_p.last_renewal_time, INTERVAL 1 DAY)
                    WHEN p.period = 'WEEK' THEN DATE_ADD(latest_p.last_renewal_time, INTERVAL 7 DAY)
                    WHEN p.period = 'MONTH' THEN DATE_ADD(latest_p.last_renewal_time, INTERVAL 1 MONTH)
                    WHEN p.period = 'YEAR' THEN DATE_ADD(latest_p.last_renewal_time, INTERVAL 1 YEAR)
                    ELSE NULL
                END AS next_billing_time
            FROM
                paypal_user_plan_record p
            INNER JOIN (
                -- 找到每个支付ID最新的那条记录
                SELECT payment_id, MAX(create_time) AS last_renewal_time
                FROM paypal_user_plan_record
                WHERE update_by != 'true'
                GROUP BY payment_id
            ) latest_p ON p.payment_id = latest_p.payment_id AND p.create_time = latest_p.last_renewal_time
            WHERE p.update_by != 'true'

            UNION ALL

            -- 从 airwallex_user_plan_record 表获取有效的、需要计算下次扣款时间的订阅
            SELECT
                a.payment_id AS subscription_id, -- 使用 payment_id 作为唯一标识
                a.amount,
                -- 计算下一次扣款时间
                CASE
                    WHEN a.period = 'DAY' THEN DATE_ADD(latest_a.last_renewal_time, INTERVAL 1 DAY)
                    WHEN a.period = 'WEEK' THEN DATE_ADD(latest_a.last_renewal_time, INTERVAL 7 DAY)
                    WHEN a.period = 'MONTH' THEN DATE_ADD(latest_a.last_renewal_time, INTERVAL 1 MONTH)
                    WHEN a.period = 'QUARTER' THEN DATE_ADD(latest_a.last_renewal_time, INTERVAL 3 MONTH)
                    WHEN a.period = 'YEAR' THEN DATE_ADD(latest_a.last_renewal_time, INTERVAL 1 YEAR)
                    ELSE NULL
                END AS next_billing_time
            FROM
                airwallex_user_plan_record a
            INNER JOIN (
                -- 找到每个支付ID最新的那条记录
                SELECT payment_id, MAX(create_time) AS last_renewal_time
                FROM airwallex_user_plan_record
                WHERE success = 1
                GROUP BY payment_id
            ) latest_a ON a.payment_id = latest_a.payment_id AND a.create_time = latest_a.last_renewal_time
            WHERE a.success = 1
        ),
        -- 将所有待扣款的订阅聚合在一起
        pending_payments AS (
            SELECT
                amount,
                next_billing_time
            FROM
                active_subscriptions
            WHERE
                next_billing_time IS NOT NULL AND next_billing_time > NOW()
        )
        -- 最后，按时间范围进行汇总
        SELECT
            SUM(CASE
                WHEN pp.next_billing_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
                THEN pp.amount
                ELSE 0
            END) AS upcoming_seven_day_amount,

            SUM(CASE
                WHEN pp.next_billing_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
                THEN pp.amount
                ELSE 0
            END) AS upcoming_thirty_day_amount
        FROM
            pending_payments pp
    </select>

    <select id="selectTimeRange" resultType="map">
        SELECT
            LEAST(min_renewal, min_plan) as min_date,
            GREATEST(max_renewal, max_plan) as max_date
        FROM (
            SELECT
                MIN(renewal_time) as min_renewal,
                MAX(renewal_time) as max_renewal
            FROM paypal_subscription_renewal
        ) renewal,
        (
            SELECT
                MIN(create_time) as min_plan,
                MAX(create_time) as max_plan
            FROM paypal_user_plan_record
        ) plan
    </select>

</mapper>
