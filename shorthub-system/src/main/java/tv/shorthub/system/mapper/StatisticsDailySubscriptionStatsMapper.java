package tv.shorthub.system.mapper;

import tv.shorthub.common.mapper.CommonMapper;
import tv.shorthub.system.domain.StatisticsDailySubscriptionStats;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 每日订阅统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface StatisticsDailySubscriptionStatsMapper extends CommonMapper<StatisticsDailySubscriptionStats>
{
    /**
     * 按多个tfid统计区间汇总
     */
    StatisticsDailySubscriptionStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按多个tfid统计区间汇总（支持时区）
     */
    StatisticsDailySubscriptionStats getSummaryByTfids(@Param("tfids") List<String> tfids, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone, @Param("appid") String appid);

    /**
     * 按创建者统计区间汇总
     */
    StatisticsDailySubscriptionStats getSummaryByCreator(@Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按创建者统计区间汇总（支持时区）
     */
    StatisticsDailySubscriptionStats getSummaryByCreator(@Param("creator") String creator, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone);

    /**
     * 按区间、appid、tfid统计汇总
     */
    StatisticsDailySubscriptionStats getRangeSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("appid") String appid, @Param("tfid") String tfid, @Param("timezone") String timezone);

    /**
     * 查询指定日期范围内的每日订阅统计数据（支持时区）
     */
    List<StatisticsDailySubscriptionStats> selectDailySubscriptionStatsWithTimezone(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("timezone") String timezone);

    /**
     * 查询订阅数据的最早和最晚日期
     */
    Map<String, Date> selectTimeRange();
}
