# Shorthub-TV 短剧平台架构

## 系统概述
Shorthub-TV 短剧平台，支持多语种、多支付、多渠道分发。基于 Spring Boot 微服务架构。

## 核心模块
- **管理端**: shorthub-admin (8080)
- **API服务**: shorthub-api (8081)  
- **广告服务**: shorthub-ad
- **统计服务**: shorthub-statistics
- **基础框架**: shorthub-framework/system/common

## 技术栈
| 分类 | 技术 | 版本 |
|---|---|---|
| 框架 | Spring Boot | 3.3.0 |
| 数据库 | MySQL | 8.0+ |
| ORM | MyBatis Plus | 3.5.7 |
| 缓存 | Redis | 7.0+ |
| 存储 | Cloudflare R2 | - |
| 队列 | Cloudflare Queue | - |
| 视频 | FFmpeg | 6.0+ |

## 核心功能

### 内容管理
- **视频处理**: FFmpeg转码、GPU加速、多码率
- **存储分发**: Cloudflare R2 + CDN
- **格式支持**: MP4、HLS自适应流

### 用户管理
- **多渠道登录**: Google、Facebook、微信
- **权限体系**: RBAC角色权限
- **会话管理**: JWT + Redis

### 支付处理
- **支付渠道**: PayPal、PayerMax、Airwallex、Google Play
- **安全保障**: RSA签名、Webhook验证
- **通知服务**: 钉钉、邮件实时通知

### 广告投放
- **投放平台**: Facebook、Google、TikTok Ads
- **转化跟踪**: 用户行为分析、效果优化

## 部署架构
- **负载均衡**: Nginx 主备集群
- **应用服务**: shorthub-admin(8080) + shorthub-api(8081)
- **数据存储**: MySQL 主从 + Redis 集群

## 基础框架公共组件

### shorthub-framework (核心框架)
- **安全认证**: `SecurityConfig` Spring Security配置、JWT Token验证
- **AOP切面**: `LogAspect` 日志记录、`DataSourceAspect` 数据源切换、`RateLimiterAspect` 限流
- **拦截器**: `RepeatSubmitInterceptor` 防重复提交、`DataSourceInterceptor` 数据源拦截
- **服务层**: `TokenService` JWT令牌管理、`SysLoginService` 登录服务、`PermissionService` 权限服务
- **配置类**: `RedisConfig` Redis配置、`ThreadPoolConfig` 线程池配置、`DruidConfig` 数据源配置

### shorthub-system (系统管理)
- **用户管理**: `SysUser` 用户实体、`SysUserMapper` 用户数据访问、用户CRUD操作
- **角色权限**: `SysRole` 角色管理、`SysMenu` 菜单权限、`SysRoleMenu` 角色菜单关联
- **部门管理**: `SysDept` 部门实体、部门树形结构管理
- **配置管理**: `SysConfig` 系统配置、`SysDictData` 字典数据管理
- **日志管理**: `SysOperLog` 操作日志、`SysLogininfor` 登录日志
- **业务实体**: App相关业务实体(用户、剧集、充值、统计等)

### shorthub-common (公共工具)
- **核心域**: `AjaxResult` 统一响应结果、`BaseEntity` 基础实体、`R` 通用返回结果
- **Redis缓存**: `RedisCache` Redis操作工具、缓存管理、分布式锁
- **工具类**: `StringUtils` 字符串工具、`ServletUtils` Servlet工具、`SecurityUtils` 安全工具
- **注解**: `@Log` 日志注解、`@DataScope` 数据权限、`@RateLimiter` 限流注解、`@RepeatSubmit` 防重复提交
- **枚举类**: `BusinessType` 业务类型、`HttpMethod` HTTP方法、`PayStatus` 支付状态
- **异常处理**: `ServiceException` 服务异常、`GlobalException` 全局异常、文件上传异常
- **分页**: `PageDomain` 分页域、`TableDataInfo` 表格数据、`TableSupport` 分页支持
- **缓存服务**: `LocalObjectCache` 本地对象缓存、`VideoUrlCacheService` 视频URL缓存

### 常用公共方法
```java
// Redis 缓存操作
redisCache.setCacheObject(key, value, timeout, TimeUnit.MINUTES);
Object obj = redisCache.getCacheObject(key);

// 统一响应结果
return AjaxResult.success(data);
return AjaxResult.error("操作失败");
return R.ok(data);

// JWT Token 管理
String token = tokenService.createToken(loginUser);
LoginUser loginUser = tokenService.getLoginUser(request);

// 字符串工具
StringUtils.isNotEmpty(str);
StringUtils.format("用户{}登录成功", username);

// 权限验证
SecurityUtils.getLoginUser();
SecurityUtils.hasPermi("system:user:list");
```
## 🎯 投放平台剧集分析 - 核心表组合

### 📊 统计核心基础表 (3张必需)

#### 1. **app_user_watch_records** (观看记录表) ⭐ 数据源
- **实体类**: AppUserWatchRecords  
- **用途**: 观看数据分析核心表
- **关键字段**:
  - user_id: 用户ID (去重统计)
  - content_id: 剧目内容ID
  - serial_number: 观看集数
  - tfid: 流量标识符 (关联推广表)
  - serial_second: 观看到第几秒 ⭐ 主要分析数据
  - appid: 应用标识 (权限控制)

#### 2. **app_promotion** (推广表) ⭐ 流量来源
- **实体类**: AppPromotion
- **用途**: 区分付费流量 vs 自然流量，权限控制
- **关键字段**:
  - tfid: 流量标识符 ⭐ **空值为" "=自然流量，有值=付费流量**
  - ad_channel: 广告渠道 (facebook, google)
  - create_by: 投放人员 (子账号权限控制)
  - video_fee_begin: 推广专属收费起始集数
  - video_every_money: 推广专属每集价格

#### 3. **app_drama_contents_serial** (剧集详情表) ⭐ 基础信息
- **实体类**: AppDramaContentsSerial
- **用途**: 提供剧集基础信息和视频时长
- **关键字段**:
  - content_id: 剧目内容ID
  - serial_number: 集数序号
  - video_seconds: 视频总时长 (计算完成率必需)
  - title: 单集标题

### 📋 辅助表 (可选)

#### 4. **app_drama_contents** (剧目内容表)
- **实体类**: AppDramaContents
- **用途**: 剧目标题和默认收费策略 
- **关键字段**: content_id, title, fee_begin, every_money

## 📊 投放平台分析指标

### 基础观看分析
  1. 每集平均观看时长: AVG(serial_second) 按剧集分组
  2. 每集观看完成率: (serial_second / video_seconds) * 100%
  3. 用户观看深度: 观看进度分布统计
  4. 剧集热度排行: 各集观看人数和观看时长

### 流量来源分析
  1. **自然流量 vs 付费流量**: 基于 tfid 是否为空区分
  2. **广告渠道效果**: Facebook vs Google 渠道观看效果对比
  3. **收费策略分析**: 默认收费 vs 推广个性化收费效果
  4. **推广转化分析**: 不同推广链接的用户观看转化率

## 🎯 流量判断逻辑

### 数据权限和流量区分
```sql
-- 自然流量判断条件
WHERE tfid = '' AND ad_channel = '' AND appid = '#{userAppId}'

-- 付费流量判断条件  
WHERE tfid != '' AND ad_channel != '' AND appid = '#{userAppId}'

-- 权限控制条件
-- ADMIN: 所有 appid 数据
-- 主账号: 指定 appid 下所有数据 
-- 子账号: 指定 appid 下自己投放的 tfid 数据 (create_by)
```

### 核心观看数据字段
- `serial_second`: 用户观看到第几秒 ⭐ 核心分析数据
- `video_seconds`: 视频总时长 (计算完成率)
- `tfid + ad_channel + appid`: 流量来源三元组判断
