package tv.shorthub.statistics.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.statistics.service.IDailyOrderStatsService;
import tv.shorthub.statistics.utils.TimeRangeUtils;
import tv.shorthub.statistics.utils.TimeZoneUtils;
import tv.shorthub.system.domain.StatisticsDailyOrderStats;
import tv.shorthub.system.service.IStatisticsDailyOrderStatsService;
import tv.shorthub.common.utils.DateUtils;
import tv.shorthub.statistics.config.StatisticsConfig;

import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 每日订单统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Service
public class DailyOrderStatsServiceImpl implements IDailyOrderStatsService {

    @Autowired
    private IStatisticsDailyOrderStatsService statisticsDailyOrderStatsService;

    @Autowired
    private StatisticsConfig statisticsConfig;

    @Override
    public void updateCurrentDayOrderStats() {
        // 获取当前日期的0点
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDay = calendar.getTime();

        log.info("[订单日统计定时任务] 开始统计当前日期订单数据 - {}", new SimpleDateFormat("yyyy-MM-dd").format(currentDay));
        updateDailyOrderStats(currentDay);
    }

    @Override
    public void updateDailyOrderStats(Date statDate) {
        try {
            log.info("[订单日统计定时任务] 开始统计日期: {}", new SimpleDateFormat("yyyy-MM-dd").format(statDate));

            // 支持多时区统计
            List<StatisticsDailyOrderStats> allResults = new ArrayList<>();
            List<String> timezones = statisticsConfig.getSupportedTimezones();
            
            for (String timezone : timezones) {
                try {
                    // 为每个时区计算该时区的"一天"对应的UTC+8时间范围
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(statDate);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MILLISECOND, 0);
                    Date timezoneStartTime = calendar.getTime();
                    calendar.add(Calendar.DATE, 1);
                    Date timezoneEndTime = calendar.getTime();
                    
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(timezoneStartTime, timezoneEndTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];
                    
                    log.debug("[订单日统计定时任务] 时区 {} - 查询时间范围: {} 到 {}", 
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));
                    
                    List<StatisticsDailyOrderStats> result = statisticsDailyOrderStatsService.getMapper().selectDailyOrderStats(adjustedStartTime, adjustedEndTime, timezone);
                    if (result != null && !result.isEmpty()) {
                        allResults.addAll(result);
                        log.info("[订单日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[订单日统计定时任务] 时区 {} 统计失败: {}", timezone, e.getMessage(), e);
                }
            }

            if (!allResults.isEmpty()) {
                statisticsDailyOrderStatsService.getMapper().batchInsertOrUpdate(allResults);
                log.info("[订单日统计定时任务] 成功更新 {} 条订单统计数据", allResults.size());
            } else {
                log.info("[订单日统计定时任务] 当前日期无订单数据");
            }
        } catch (Exception e) {
            log.error("[订单日统计定时任务] 统计失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算指定时间范围内的每日订单统计数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据列表
     */
    private List<StatisticsDailyOrderStats> calculateDailyOrderStats(Date startTime, Date endTime) {
        return executeStatisticsQuery(startTime, endTime);
    }

    /**
     * 执行统计查询
     */
    private List<StatisticsDailyOrderStats> executeStatisticsQuery(Date startTime, Date endTime) {
        try {
            // 支持多时区统计
            List<StatisticsDailyOrderStats> allResults = new ArrayList<>();
            List<String> timezones = statisticsConfig.getSupportedTimezones();
            
            for (String timezone : timezones) {
                try {
                    // 根据目标时区调整查询时间范围
                    Date[] adjustedTimeRange = TimeZoneUtils.adjustTimeRangeForQuery(startTime, endTime, timezone);
                    Date adjustedStartTime = adjustedTimeRange[0];
                    Date adjustedEndTime = adjustedTimeRange[1];
                    
                    log.debug("[订单日统计定时任务] 时区 {} - 原始时间范围: {} 到 {}", 
                        timezone, 
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
                    log.debug("[订单日统计定时任务] 时区 {} - 调整后时间范围: {} 到 {}", 
                        timezone,
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedStartTime),
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(adjustedEndTime));
                    
                    List<StatisticsDailyOrderStats> result = statisticsDailyOrderStatsService.getMapper().selectDailyOrderStats(adjustedStartTime, adjustedEndTime, timezone);
                    if (result != null && !result.isEmpty()) {
                        allResults.addAll(result);
                        log.info("[订单日统计定时任务] 时区 {} 查询到 {} 条统计数据", timezone, result.size());
                    }
                } catch (Exception e) {
                    log.error("[订单日统计定时任务] 时区 {} 查询失败: {}", timezone, e.getMessage());
                }
            }
            
            log.info("[订单日统计定时任务] 总共查询到 {} 条统计数据", allResults.size());
            return allResults;
        } catch (Exception e) {
            log.error("[订单日统计定时任务] 查询统计数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public String batchUpdateOrderStats(Date startDate, Date endDate) {
        try {
            log.info("[订单日统计批量任务] 开始全量统计订单数据 - 时间范围: {} 到 {}", new SimpleDateFormat("yyyy-MM-dd").format(startDate), new SimpleDateFormat("yyyy-MM-dd").format(endDate));
            int totalDays = 0;
            int successDays = 0;
            int totalRecords = 0;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(endDate);
            endCalendar.set(Calendar.HOUR_OF_DAY, 0);
            endCalendar.set(Calendar.MINUTE, 0);
            endCalendar.set(Calendar.SECOND, 0);
            endCalendar.set(Calendar.MILLISECOND, 0);
            while (!calendar.getTime().after(endCalendar.getTime())) {
                try {
                    Date currentDay = calendar.getTime();
                    Date nextDay = DateUtils.addDays(currentDay, 1);
                    List<StatisticsDailyOrderStats> dailyStats = executeStatisticsQuery(currentDay, nextDay);
                    if (dailyStats != null && !dailyStats.isEmpty()) {
                        statisticsDailyOrderStatsService.getMapper().batchInsertOrUpdate(dailyStats);
                        totalRecords += dailyStats.size();
                        successDays++;
                    }
                    totalDays++;
                    if (totalDays % 30 == 0) {
                        log.info("[订单日统计批量任务] 进度: {}/{} 天，已更新 {} 条记录", successDays, totalDays, totalRecords);
                    }
                } catch (Exception e) {
                    log.error("[订单日统计批量任务] 统计日期 {} 失败: {}", new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()), e.getMessage());
                }
                calendar.add(Calendar.DATE, 1);
            }
            String result = String.format("订单日统计完成！共处理 %d 天，成功 %d 天，更新 %d 条记录", totalDays, successDays, totalRecords);
            log.info("[订单日统计批量任务] {}", result);
            return result;
        } catch (Exception e) {
            String error = "订单日批量统计失败: " + e.getMessage();
            log.error("[订单日统计批量任务] {}", error, e);
            return error;
        }
    }

    @Override
    public String batchUpdateRecentOrderStats(int days) {
        Date endDate = new Date();
        Date startDate = DateUtils.addDays(endDate, -days);
        return batchUpdateOrderStats(startDate, endDate);
    }

    @Override
    public String batchUpdateAllStats() {
        log.info("[订单日全量统计] 开始执行...");
        Map<String, Date> timeRange = statisticsDailyOrderStatsService.getMapper().selectTimeRange();
        Date minDate = timeRange.get("min_date");
        Date maxDate = timeRange.get("max_date");
        if (minDate == null || maxDate == null) {
            return "[订单日全量统计] 未找到任何订单数据，任务结束。";
        }
        log.info("[订单日全量统计] 数据时间范围: {} 到 {}", minDate, maxDate);
        return batchUpdateOrderStats(minDate, maxDate);
    }
} 