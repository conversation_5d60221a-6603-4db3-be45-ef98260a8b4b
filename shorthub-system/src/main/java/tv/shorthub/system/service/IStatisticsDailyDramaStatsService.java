package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsDailyDramaStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.Date;
import java.util.List;
import tv.shorthub.system.domain.StatisticsDailyDramaStats;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 每日剧集分析统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IStatisticsDailyDramaStatsService extends IBaseService<StatisticsDailyDramaStats>
{
    /**
     * 查询每日剧集分析统计
     *
     * @param id 每日剧集分析统计主键
     * @return 每日剧集分析统计
     */
    public StatisticsDailyDramaStats getById(Long id);

    /**
     * 查询每日剧集分析统计数据汇总
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计数据汇总
     */
    public StatisticsDailyDramaStats getSummary(StatisticsDailyDramaStats query);

    /**
     * 查询每日剧集分析统计列表
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计集合
     */
    public List<StatisticsDailyDramaStats> selectList(StatisticsDailyDramaStats query);

    /**
     * 新增每日剧集分析统计
     *
     * @param statisticsDailyDramaStats 每日剧集分析统计
     * @return 结果
     */
    public int insert(StatisticsDailyDramaStats statisticsDailyDramaStats);

    /**
     * 修改每日剧集分析统计
     *
     * @param statisticsDailyDramaStats 每日剧集分析统计
     * @return 结果
     */
    public int update(StatisticsDailyDramaStats statisticsDailyDramaStats);

    /**
     * 批量删除每日剧集分析统计
     *
     * @param ids 需要删除的每日剧集分析统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除每日剧集分析统计信息
     *
     * @param id 每日剧集分析统计主键
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 查询自定义分析数据
     *
     * @param query 每日剧集分析统计
     * @return 每日剧集分析统计集合
     */
    public List<StatisticsDailyDramaStats> summary(SummaryRequest query);

    StatisticsDailyDramaStats allSummary(SummaryRequest query);

    StatisticsDailyDramaStatsMapper getMapper();

    // 新增业务方法

    /**
     * 根据时间范围查询每日剧集统计数据
     */
    List<StatisticsDailyDramaStats> selectDailyDramaStats(Date startTime, Date endTime, String timezone);

    /**
     * 根据时间范围获取剧集统计汇总数据
     */
    StatisticsDailyDramaStats getRangeSummary(Date startTime, Date endTime, String appid, String tfid, String timezone);

    /**
     * 根据时间范围查询剧集统计列表
     */
    List<StatisticsDailyDramaStats> selectListByTimeRange(Date startTime, Date endTime, StatisticsDailyDramaStats query);

    /**
     * 根据 tfid 列表获取汇总数据
     */
    StatisticsDailyDramaStats getSummaryByTfids(List<String> tfids, Date startTime, Date endTime, String appid, String timezone);

    /**
     * 根据创建者获取汇总数据
     */
    StatisticsDailyDramaStats getSummaryByCreator(String creatorName, Date startTime, Date endTime, String timezone);
}
