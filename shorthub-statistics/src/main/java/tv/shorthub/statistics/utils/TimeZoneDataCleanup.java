package tv.shorthub.statistics.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tv.shorthub.system.service.IStatisticsDailyOrderStatsService;

/**
 * 时区数据清理工具
 * 用于清理错误的时区统计数据
 */
@Slf4j
@Component
public class TimeZoneDataCleanup {

    @Autowired
    private IStatisticsDailyOrderStatsService statisticsDailyOrderStatsService;

    /**
     * 清理非UTC+8时区的统计数据
     * 因为系统只在UTC+8时区运行，其他时区的数据可能是错误的
     */
    public void cleanupNonUTC8Data() {
        try {
            log.info("[时区数据清理] 开始清理非UTC+8时区的统计数据");
            
            // 删除所有非UTC+8时区的数据
            int deletedCount = statisticsDailyOrderStatsService.getMapper()
                .delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>()
                    .ne("timezone", "UTC+8"));
            
            log.info("[时区数据清理] 成功删除 {} 条非UTC+8时区的统计数据", deletedCount);
            
        } catch (Exception e) {
            log.error("[时区数据清理] 清理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理指定时区的统计数据
     */
    public void cleanupTimezoneData(String timezone) {
        try {
            log.info("[时区数据清理] 开始清理时区 {} 的统计数据", timezone);
            
            int deletedCount = statisticsDailyOrderStatsService.getMapper()
                .delete(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>()
                    .eq("timezone", timezone));
            
            log.info("[时区数据清理] 成功删除时区 {} 的 {} 条统计数据", timezone, deletedCount);
            
        } catch (Exception e) {
            log.error("[时区数据清理] 清理时区 {} 失败: {}", timezone, e.getMessage(), e);
        }
    }
}
