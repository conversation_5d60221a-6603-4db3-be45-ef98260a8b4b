<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tv.shorthub.system.mapper.StatisticsDailySubscriptionStatsMapper">

    <resultMap type="StatisticsDailySubscriptionStats" id="StatisticsDailySubscriptionStatsResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="appid"    column="appid"    />
        <result property="tfid"    column="tfid"    />
        <result property="newSubscriptionOrderCount"    column="new_subscription_order_count"    />
        <result property="newSubscriptionOrderAmount"    column="new_subscription_order_amount"    />
        <result property="successfulRenewalCount"    column="successful_renewal_count"    />
        <result property="successfulRenewalAmount"    column="successful_renewal_amount"    />
        <result property="totalOrderCount"    column="total_order_count"    />
        <result property="totalOrderAmount"    column="total_order_amount"    />
        <result property="timezone"    column="timezone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStatisticsDailySubscriptionStatsVo">
        select id, stat_date, appid, tfid, new_subscription_order_count, new_subscription_order_amount, successful_renewal_count, successful_renewal_amount, total_order_count, total_order_amount, timezone, create_by, create_time, update_by, update_time from statistics_daily_subscription_stats
    </sql>

    <insert id="batchInsertOrUpdate">
        <foreach item="item" index="index" collection="list" separator=";">
            insert into statistics_daily_subscription_stats
            <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="item.statDate != null">stat_date,</if>
                        <if test="item.appid != null and item.appid != ''">appid,</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid,</if>
                        <if test="item.newSubscriptionOrderCount != null">new_subscription_order_count,</if>
                        <if test="item.newSubscriptionOrderAmount != null">new_subscription_order_amount,</if>
                        <if test="item.successfulRenewalCount != null">successful_renewal_count,</if>
                        <if test="item.successfulRenewalAmount != null">successful_renewal_amount,</if>
                        <if test="item.totalOrderCount != null">total_order_count,</if>
                        <if test="item.totalOrderAmount != null">total_order_amount,</if>
                        <if test="item.timezone != null">timezone,</if>
                        <if test="item.createBy != null">create_by,</if>
                        <if test="item.createTime != null">create_time,</if>
                        <if test="item.updateBy != null">update_by,</if>
                        <if test="item.updateTime != null">update_time,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="item.statDate != null">#{item.statDate},</if>
                        <if test="item.appid != null and item.appid != ''">#{item.appid},</if>
                        <if test="item.tfid != null and item.tfid != ''">#{item.tfid},</if>
                        <if test="item.newSubscriptionOrderCount != null">#{item.newSubscriptionOrderCount},</if>
                        <if test="item.newSubscriptionOrderAmount != null">#{item.newSubscriptionOrderAmount},</if>
                        <if test="item.successfulRenewalCount != null">#{item.successfulRenewalCount},</if>
                        <if test="item.successfulRenewalAmount != null">#{item.successfulRenewalAmount},</if>
                        <if test="item.totalOrderCount != null">#{item.totalOrderCount},</if>
                        <if test="item.totalOrderAmount != null">#{item.totalOrderAmount},</if>
                        <if test="item.timezone != null">#{item.timezone},</if>
                        <if test="item.createBy != null">#{item.createBy},</if>
                        <if test="item.createTime != null">#{item.createTime},</if>
                        <if test="item.updateBy != null">#{item.updateBy},</if>
                        <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            on duplicate key update
            <trim suffixOverrides=",">
                        <if test="item.statDate != null">stat_date = values(stat_date),</if>
                        <if test="item.appid != null and item.appid != ''">appid = values(appid),</if>
                        <if test="item.tfid != null and item.tfid != ''">tfid = values(tfid),</if>
                        <if test="item.newSubscriptionOrderCount != null">new_subscription_order_count = values(new_subscription_order_count),</if>
                        <if test="item.newSubscriptionOrderAmount != null">new_subscription_order_amount = values(new_subscription_order_amount),</if>
                        <if test="item.successfulRenewalCount != null">successful_renewal_count = values(successful_renewal_count),</if>
                        <if test="item.successfulRenewalAmount != null">successful_renewal_amount = values(successful_renewal_amount),</if>
                        <if test="item.totalOrderCount != null">total_order_count = values(total_order_count),</if>
                        <if test="item.totalOrderAmount != null">total_order_amount = values(total_order_amount),</if>
                        <if test="item.timezone != null">timezone = values(timezone),</if>
                        <if test="item.createBy != null">create_by = values(create_by),</if>
                        <if test="item.createTime != null">create_time = values(create_time),</if>
                        <if test="item.updateBy != null">update_by = values(update_by),</if>
                        <if test="item.updateTime != null">update_time = values(update_time),</if>
            </trim>
        </foreach>

    </insert>

    <select id="getSummary" parameterType="StatisticsDailySubscriptionStats" resultMap="StatisticsDailySubscriptionStatsResult">
        select
            max(id) as id
        from statistics_daily_subscription_stats
        <where>
                    <if test="statDate != null "> and stat_date = #{statDate}</if>
                    <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
                    <if test="tfid != null  and tfid != ''"> and tfid = #{tfid}</if>
                    <if test="newSubscriptionOrderCount != null "> and new_subscription_order_count = #{newSubscriptionOrderCount}</if>
                    <if test="newSubscriptionOrderAmount != null "> and new_subscription_order_amount = #{newSubscriptionOrderAmount}</if>
                    <if test="successfulRenewalCount != null "> and successful_renewal_count = #{successfulRenewalCount}</if>
                    <if test="successfulRenewalAmount != null "> and successful_renewal_amount = #{successfulRenewalAmount}</if>
                    <if test="totalOrderCount != null "> and total_order_count = #{totalOrderCount}</if>
                    <if test="totalOrderAmount != null "> and total_order_amount = #{totalOrderAmount}</if>
                </where>
    </select>




    <!-- 按多个tfid统计区间汇总 -->
    <select id="getSummaryByTfids" resultMap="StatisticsDailySubscriptionStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            SUM(new_subscription_order_count) AS new_subscription_order_count,
            SUM(new_subscription_order_amount) AS new_subscription_order_amount,
            SUM(successful_renewal_count) AS successful_renewal_count,
            SUM(successful_renewal_amount) AS successful_renewal_amount,
            SUM(total_order_count) AS total_order_count,
            SUM(total_order_amount) AS total_order_amount
        FROM statistics_daily_subscription_stats
        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        <if test="tfids != null and tfids.size() > 0">
            AND tfid IN
            <foreach collection="tfids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
    </select>

    <!-- 按创建者统计区间汇总（主账号） -->
    <select id="getSummaryByCreator" resultMap="StatisticsDailySubscriptionStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            SUM(new_subscription_order_count) AS new_subscription_order_count,
            SUM(new_subscription_order_amount) AS new_subscription_order_amount,
            SUM(successful_renewal_count) AS successful_renewal_count,
            SUM(successful_renewal_amount) AS successful_renewal_amount,
            SUM(total_order_count) AS total_order_count,
            SUM(total_order_amount) AS total_order_amount
        FROM statistics_daily_subscription_stats
        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        AND appid IN (
            SELECT appid FROM app_promotion WHERE creator = #{creator}
        )
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <!-- 按区间、appid、tfid统计汇总 -->
    <select id="getRangeSummary" resultMap="StatisticsDailySubscriptionStatsResult">
        SELECT
            NULL as id,
            NULL as stat_date,
            NULL as appid,
            NULL as tfid,
            SUM(new_subscription_order_count) AS new_subscription_order_count,
            SUM(new_subscription_order_amount) AS new_subscription_order_amount,
            SUM(successful_renewal_count) AS successful_renewal_count,
            SUM(successful_renewal_amount) AS successful_renewal_amount,
            SUM(total_order_count) AS total_order_count,
            SUM(total_order_amount) AS total_order_amount
        FROM statistics_daily_subscription_stats
        WHERE stat_date BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
        <if test="tfid != null and tfid != ''">
            AND tfid = #{tfid}
        </if>
        <if test="timezone != null and timezone != ''">
            AND timezone = #{timezone}
        </if>
    </select>

    <!-- 查询指定日期范围内的每日订阅统计数据（支持时区） -->
    <select id="selectDailySubscriptionStatsWithTimezone" resultMap="StatisticsDailySubscriptionStatsResult">
        WITH order_subscriptions AS (
            -- 订单表中的首次订阅数据，按IP去重统计
            SELECT
                DATE_FORMAT(aoi.pay_time, '%Y-%m-%d') AS stat_date,
                COALESCE(aoi.appid, 'default') AS appid,
                COALESCE(aoi.tfid, '') as tfid,
                -- 按IP去重统计首次订阅人数
                COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp'))) AS new_subscription_count,
                SUM(aoi.order_amount) AS new_subscription_amount
            FROM app_order_info aoi
            WHERE aoi.pay_time BETWEEN #{startTime} AND #{endTime}
              AND aoi.order_status = 1
              AND aoi.pay_type = '2'
              AND JSON_UNQUOTE(JSON_EXTRACT(aoi.extend_json, '$.clientIp')) IS NOT NULL
            GROUP BY stat_date, appid, tfid
        ),
        paypal_subscriptions AS (
            -- PayPal订阅续订数据 (paypal_subscription_renewal表) - 只统计续订，不统计首次
            SELECT
                DATE_FORMAT(psr.renewal_time, '%Y-%m-%d') AS stat_date,
                COALESCE(aoi.appid, 'default') AS appid,
                COALESCE(aoi.tfid, '') as tfid,
                -- 首次订阅改为从订单表统计，这里只统计续订
                0 AS new_subscription_count,
                0 AS new_subscription_amount,
                COUNT(CASE WHEN psr.cycle_number > 1 THEN psr.id END) AS renewal_count,
                SUM(CASE WHEN psr.cycle_number > 1 THEN psr.amount ELSE 0 END) AS renewal_amount
            FROM paypal_subscription_renewal psr
                     JOIN app_order_info aoi ON psr.order_no = aoi.order_no
            WHERE psr.renewal_time BETWEEN #{startTime} AND #{endTime}
              AND psr.status = 'ACTIVE'
            GROUP BY stat_date, appid, tfid
        ),
             plan_subscriptions AS (
                 -- PayPal签约数据 (paypal_user_plan_record表)
                 -- 签约表中所有记录都是续订，period_num>=1都是续订
                 SELECT
                     DATE_FORMAT(pupr.create_time, '%Y-%m-%d') AS stat_date,
                     COALESCE(aoi.appid, 'default') AS appid,
                     COALESCE(aoi.tfid, '') as tfid,
                     -- 签约表中没有首次订阅，所有记录都是续订
                     0 AS plan_new_subscription_count,
                     0 AS plan_new_subscription_amount,
                     -- 签约表中所有记录都是续订（period_num >= 1）
                     COUNT(pupr.id) AS plan_renewal_count,
                     SUM(pupr.amount) AS plan_renewal_amount
                 FROM paypal_user_plan_record pupr
                          JOIN app_order_info aoi ON pupr.order_no = aoi.order_no
                 WHERE pupr.create_time BETWEEN #{startTime} AND #{endTime}
                   AND pupr.update_by != 'true'
                 GROUP BY stat_date, appid, tfid
             ),
             airwallex_plan_subscriptions AS (
                 -- Airwallex签约数据 (airwallex_user_plan_record表)
                 -- 跟PayPal签约逻辑一样：period_num > 1 就是续订
                 SELECT
                     DATE_FORMAT(aupr.create_time, '%Y-%m-%d') AS stat_date,
                     COALESCE(aoi.appid, 'default') AS appid,
                     COALESCE(aoi.tfid, '') as tfid,
                     -- 签约表中没有首次订阅，所有记录都是续订
                     0 AS airwallex_new_subscription_count,
                     0 AS airwallex_new_subscription_amount,
                     -- 签约表中所有成功记录都是续订
                     COUNT(CASE WHEN aupr.success = 1 THEN aupr.id END) AS airwallex_renewal_count,
                     SUM(CASE WHEN aupr.success = 1 THEN aupr.amount ELSE 0 END) AS airwallex_renewal_amount
                 FROM airwallex_user_plan_record aupr
                          JOIN app_order_info aoi ON aupr.order_no = aoi.order_no
                 WHERE aupr.create_time BETWEEN #{startTime} AND #{endTime}
                 GROUP BY stat_date, appid, tfid
             ),
             active_dates_grid AS (
                 -- 构建活跃日期、AppID和tfid的基础网格
                 SELECT stat_date, appid, tfid FROM order_subscriptions
                 UNION ALL
                 SELECT stat_date, appid, tfid FROM paypal_subscriptions
                 UNION ALL
                 SELECT stat_date, appid, tfid FROM plan_subscriptions
                 UNION ALL
                 SELECT stat_date, appid, tfid FROM airwallex_plan_subscriptions
             )
        -- 最终查询：按tfid分组统计订阅数据
        SELECT
            STR_TO_DATE(ag.stat_date, '%Y-%m-%d') as stat_date,
            ag.appid,
            COALESCE(ag.tfid, '') as tfid,
            -- 首次订阅：来自订单表的订阅类型订单，按IP去重
            COALESCE(os.new_subscription_count, 0) AS new_subscription_order_count,
            COALESCE(os.new_subscription_amount, 0.00) AS new_subscription_order_amount,
            -- 续订：paypal_subscription_renewal表的cycle_number>=2 + paypal_user_plan_record表的所有记录 + airwallex_user_plan_record表的period_num>1记录
            (COALESCE(ps.renewal_count, 0) + COALESCE(pls.plan_renewal_count, 0) + COALESCE(aps.airwallex_renewal_count, 0)) AS successful_renewal_count,
            (COALESCE(ps.renewal_amount, 0.00) + COALESCE(pls.plan_renewal_amount, 0.00) + COALESCE(aps.airwallex_renewal_amount, 0.00)) AS successful_renewal_amount,
            -- 总数：首次订阅 + 续订
            (COALESCE(os.new_subscription_count, 0) + COALESCE(ps.renewal_count, 0) + COALESCE(pls.plan_renewal_count, 0) + COALESCE(aps.airwallex_renewal_count, 0)) AS total_order_count,
            (COALESCE(os.new_subscription_amount, 0.00) + COALESCE(ps.renewal_amount, 0.00) + COALESCE(pls.plan_renewal_amount, 0.00) + COALESCE(aps.airwallex_renewal_amount, 0.00)) AS total_order_amount,
            #{timezone} as timezone,
            'system' as create_by,
            NOW() as create_time,
            'system' as update_by,
            NOW() as update_time
        FROM active_dates_grid ag
                 LEFT JOIN order_subscriptions os ON ag.stat_date = os.stat_date AND ag.appid = os.appid AND COALESCE(ag.tfid, '') = COALESCE(os.tfid, '')
                 LEFT JOIN paypal_subscriptions ps ON ag.stat_date = ps.stat_date AND ag.appid = ps.appid AND COALESCE(ag.tfid, '') = COALESCE(ps.tfid, '')
                 LEFT JOIN plan_subscriptions pls ON ag.stat_date = pls.stat_date AND ag.appid = pls.appid AND COALESCE(ag.tfid, '') = COALESCE(pls.tfid, '')
                 LEFT JOIN airwallex_plan_subscriptions aps ON ag.stat_date = aps.stat_date AND ag.appid = aps.appid AND COALESCE(ag.tfid, '') = COALESCE(aps.tfid, '')
        ORDER BY ag.stat_date, ag.appid, ag.tfid
    </select>

    <!-- 查询订阅数据的最早和最晚日期 -->
    <select id="selectTimeRange" resultType="java.util.HashMap">
        SELECT MIN(create_time) AS min_date, MAX(create_time) AS max_date FROM statistics_daily_subscription_stats
    </select>

</mapper>
