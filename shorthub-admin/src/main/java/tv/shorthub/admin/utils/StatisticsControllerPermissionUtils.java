package tv.shorthub.admin.utils;

import tv.shorthub.common.core.domain.AjaxResult;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.mapper.AppPromotionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Date;
import java.util.function.Function;

/**
 * 统计Controller权限工具类
 * 统一处理统计Controller的权限验证和数据过滤逻辑
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Component
public class StatisticsControllerPermissionUtils {

    @Autowired
    private AppPromotionMapper appPromotionMapper;

    /**
     * 检查用户是否只有投手权限
     * 
     * @return true 如果只是投手，false 如果有更高权限
     */
    public boolean isDeliverOnly() {
        try {
            // 如果是主账号，可以看到所有数据
            if (SecurityUtils.isBusinessAdmin()) {
                return false;
            }
            // 否则就是投手，只能看到自己的数据
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取用户权限信息
     * 
     * @return 权限信息对象
     */
    public UserPermissionInfo getUserPermissionInfo() {
        String currentUsername = SecurityUtils.getUsername();
        boolean deliverOnly = isDeliverOnly();
        boolean isBusinessAdmin = SecurityUtils.isBusinessAdmin() && !SecurityUtils.isSystemAdmin();
        boolean isSystemAdmin = SecurityUtils.isSystemAdmin();
        
        return new UserPermissionInfo(currentUsername, deliverOnly, isBusinessAdmin, isSystemAdmin);
    }

    /**
     * 处理按投手筛选的统计查询
     * 
     * @param delivererName 投手名称
     * @param tfid 可选的tfid过滤
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param queryFunction 查询函数，接收tfid列表和时间范围
     * @param emptyResultSupplier 空结果供应商
     * @return 查询结果
     */
    public <T> AjaxResult handleDelivererQuery(String delivererName, String tfid, 
                                               Date startTime, Date endTime,
                                               Function<DelivererQueryParams, T> queryFunction,
                                               T emptyResult) {
        UserPermissionInfo userInfo = getUserPermissionInfo();
        
        if (StringUtils.isEmpty(delivererName)) {
            return null; // 表示不是按投手筛选的查询
        }
        
        if (userInfo.deliverOnly) {
            // 投手不能查看其他人的数据
            return AjaxResult.error("您没有权限查看其他投手的数据");
        }
        
        List<String> delivererTfids = appPromotionMapper.selectTfidsByCreator(delivererName);
        if (CollectionUtils.isEmpty(delivererTfids)) {
            return AjaxResult.success(emptyResult);
        }
        
        // 如果在选定投手的同时，还指定了单个tfid
        if (StringUtils.isNotEmpty(tfid)) {
            // 校验该tfid是否属于该投手
            if (delivererTfids.contains(tfid)) {
                // 如果合法，则查询范围缩小到这单个tfid
                delivererTfids = List.of(tfid);
            } else {
                // 非法组合，返回空结果
                return AjaxResult.success(emptyResult);
            }
        }

        DelivererQueryParams params = new DelivererQueryParams(delivererTfids, startTime, endTime);
        T summary = queryFunction.apply(params);
        AjaxResult result = AjaxResult.success(summary);
        result.put("showSensitiveData", true);
        return result;
    }

    /**
     * 处理默认的统计查询（无特定筛选条件）
     * 
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param delivererQueryFunction 投手查询函数
     * @param businessAdminQueryFunction 主账号查询函数
     * @param systemAdminQueryFunction 系统管理员查询函数
     * @param emptyResult 空结果
     * @return 查询结果
     */
    public <T> AjaxResult handleDefaultQuery(String appid, String tfid, 
                                           Date startTime, Date endTime,
                                           Function<DelivererQueryParams, T> delivererQueryFunction,
                                           Function<BusinessAdminQueryParams, T> businessAdminQueryFunction,
                                           Function<SystemAdminQueryParams, T> systemAdminQueryFunction,
                                           T emptyResult) {
        UserPermissionInfo userInfo = getUserPermissionInfo();
        
        // 默认行为：根据用户角色显示不同的数据
        if (StringUtils.isEmpty(appid) && StringUtils.isEmpty(tfid)) {
            if (userInfo.deliverOnly) {
                // 投手：看自己的所有推广链接数据
                List<String> tfids = appPromotionMapper.selectTfidsByCreator(userInfo.currentUsername);
                if (CollectionUtils.isEmpty(tfids)) {
                    return AjaxResult.success(emptyResult);
                }
                DelivererQueryParams params = new DelivererQueryParams(tfids, startTime, endTime);
                T summary = delivererQueryFunction.apply(params);
                AjaxResult result = AjaxResult.success(summary);
                result.put("showSensitiveData", false);
                return result;
            } else if (userInfo.isBusinessAdmin) {
                // 主账号：看自己appid下的所有数据（自然流量+所有投放）
                BusinessAdminQueryParams params = new BusinessAdminQueryParams(userInfo.currentUsername, startTime, endTime);
                T summary = businessAdminQueryFunction.apply(params);
                AjaxResult result = AjaxResult.success(summary);
                result.put("showSensitiveData", true);
                return result;
            } else if (userInfo.isSystemAdmin) {
                // 系统管理员：看所有数据
                SystemAdminQueryParams params = new SystemAdminQueryParams(startTime, endTime, appid);
                T summary = systemAdminQueryFunction.apply(params);
                AjaxResult result = AjaxResult.success(summary);
                result.put("showSensitiveData", true);
                return result;
            }
        }
        
        return null; // 表示需要继续处理其他情况
    }

    /**
     * 处理其他情况的统计查询
     * 
     * @param appid 应用ID
     * @param tfid 推广链接ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param generalQueryFunction 通用查询函数
     * @return 查询结果
     */
    public <T> AjaxResult handleOtherQuery(String appid, String tfid, 
                                         Date startTime, Date endTime,
                                         Function<GeneralQueryParams, T> generalQueryFunction) {
        UserPermissionInfo userInfo = getUserPermissionInfo();
        
        // 其他情况：按指定的appid/tfid查询
        if (userInfo.deliverOnly) {
            // 投手只能查看自己的tfid
            if (StringUtils.isNotEmpty(tfid)) {
                List<String> userTfids = appPromotionMapper.selectTfidsByCreator(userInfo.currentUsername);
                if (!userTfids.contains(tfid)) {
                    return AjaxResult.error("您没有权限查看该tfid的数据");
                }
            } else {
                return AjaxResult.error("投手必须指定tfid参数");
            }
        }
        
        GeneralQueryParams params = new GeneralQueryParams(startTime, endTime, appid, tfid);
        T summary = generalQueryFunction.apply(params);
        AjaxResult result = AjaxResult.success(summary);
        result.put("showSensitiveData", !userInfo.deliverOnly);
        return result;
    }

    /**
     * 用户权限信息
     */
    public static class UserPermissionInfo {
        public final String currentUsername;
        public final boolean deliverOnly;
        public final boolean isBusinessAdmin;
        public final boolean isSystemAdmin;

        public UserPermissionInfo(String currentUsername, boolean deliverOnly, 
                                boolean isBusinessAdmin, boolean isSystemAdmin) {
            this.currentUsername = currentUsername;
            this.deliverOnly = deliverOnly;
            this.isBusinessAdmin = isBusinessAdmin;
            this.isSystemAdmin = isSystemAdmin;
        }
    }

    /**
     * 投手查询参数
     */
    public static class DelivererQueryParams {
        public final List<String> tfids;
        public final Date startTime;
        public final Date endTime;

        public DelivererQueryParams(List<String> tfids, Date startTime, Date endTime) {
            this.tfids = tfids;
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

    /**
     * 主账号查询参数
     */
    public static class BusinessAdminQueryParams {
        public final String creatorName;
        public final Date startTime;
        public final Date endTime;

        public BusinessAdminQueryParams(String creatorName, Date startTime, Date endTime) {
            this.creatorName = creatorName;
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

    /**
     * 系统管理员查询参数
     */
    public static class SystemAdminQueryParams {
        public final Date startTime;
        public final Date endTime;
        public final String appid;

        public SystemAdminQueryParams(Date startTime, Date endTime, String appid) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.appid = appid;
        }
    }

    /**
     * 通用查询参数
     */
    public static class GeneralQueryParams {
        public final Date startTime;
        public final Date endTime;
        public final String appid;
        public final String tfid;

        public GeneralQueryParams(Date startTime, Date endTime, String appid, String tfid) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.appid = appid;
            this.tfid = tfid;
        }
    }
}