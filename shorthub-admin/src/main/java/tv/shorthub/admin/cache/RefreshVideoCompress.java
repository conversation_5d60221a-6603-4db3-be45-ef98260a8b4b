package tv.shorthub.admin.cache;

import java.io.File;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;
import tv.shorthub.admin.service.VideoCompress;
import tv.shorthub.common.annotation.CacheRefresh;
import tv.shorthub.common.core.cache.refresh.RefreshService;
import tv.shorthub.common.core.oss.ProviderOSS;
import tv.shorthub.common.core.oss.S3Uploader;
import tv.shorthub.system.domain.AppDramaContentsSerial;
import tv.shorthub.system.domain.AppDramaContentsSerialUrl;
import tv.shorthub.system.service.IAppDramaContentsSerialService;
import tv.shorthub.system.service.IAppDramaContentsSerialUrlService;

@CacheRefresh(key = "refresh_video_compress", seconds = 10)
@Component
@Slf4j
public class RefreshVideoCompress implements RefreshService {

    @Autowired
    VideoCompress videoCompress;

    @Autowired
    IAppDramaContentsSerialService appDramaContentsSerialService;

    @Autowired
    IAppDramaContentsSerialUrlService appDramaContentsSerialUrlService;

    @Autowired
    ProviderOSS providerOSS;

    @Autowired
    S3Uploader s3Uploader;

    @Value("${video.temp.path:/tmp/video}")
    private String tempVideoPath;

    private static final String COMPRESSED_DIR = "compressed";
    public static final int URL_EXPIRY_HOURS = 24;

    @Override
    public void start() {
        try {
            // 1. 处理未压缩的视频
            log.info("准备处理视频压缩任务");
            processUncompressedVideos();

            // 2. 已压缩视频转换为hls格式
//            processCompressedVideosToHls();

            // 3. 更新已压缩视频的时长
            updateCompressedVideoDurations();

        } catch (Exception e) {
            log.error("视频压缩和URL维护过程中发生错误", e);
        }
    }

    private void processCompressedVideosToHls() {
        // 查询已压缩的mp4视频
        QueryWrapper<AppDramaContentsSerialUrl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compress_type", "compressed")
                .eq("video_format", "mp4")
        ;

        List<AppDramaContentsSerialUrl> uncompressedVideos = appDramaContentsSerialUrlService.getMapper().selectList(queryWrapper);

        for (AppDramaContentsSerialUrl video : uncompressedVideos) {
            long time = System.currentTimeMillis();
            try {
                String originalUrl = video.getVideoUrl();
                String storagePath = video.getStoragePath();

                if (originalUrl == null || originalUrl.isEmpty() || storagePath == null || storagePath.isEmpty()) {
                    continue;
                }

                // 检查是否已存在hls版本
                QueryWrapper<AppDramaContentsSerialUrl> urlQuery = new QueryWrapper<>();
                urlQuery.eq("serial_id", video.getSerialId())
                        .eq("compress_type", "compressed")
                        .eq("video_format", "hls")
                ;

                if (appDramaContentsSerialUrlService.getMapper().selectCount(urlQuery) > 0) {
                    continue;
                }


                // 下载原始视频
                String tempInputPath = downloadVideo(originalUrl);
                if (tempInputPath == null) {
                    continue;
                }

                // 生成压缩后的文件路径
                String compressedPath = generateCompressedPath(storagePath);

                // HLS 压缩
                String tempHlsDir = tempVideoPath + "/" + UUID.randomUUID();
                String r2BasePath = compressedPath.replace(".mp4", "").replaceAll("compressed/compressed", "compressed/" + video.getSerialNumber().toString()); // 例如 compressed/xxx
                VideoCompress.HlsResult hlsResult = videoCompress.compressToHls(tempInputPath, tempHlsDir, r2BasePath + "/");

                // 上传 m3u8
                String r2M3u8Path = r2BasePath + "/index.m3u8";
                providerOSS.uploadToPrivate(r2M3u8Path, hlsResult.m3u8Path);

                // 上传 ts 分片
                for (String tsPath : hlsResult.tsPaths) {
                    String tsName = new File(tsPath).getName();
                    String r2TsPath = r2BasePath + "/" + tsName;
                    providerOSS.uploadToPrivate(r2TsPath, tsPath);
                }

                // 使用已签名的URL
                String compressedUrl = hlsResult.signedM3u8Url;

                // 创建压缩视频的URL记录
                AppDramaContentsSerialUrl compressedUrlEntity = new AppDramaContentsSerialUrl();
                File originalFile = new File(tempInputPath);
                compressedUrlEntity.setVideoSize(originalFile.length());
                compressedUrlEntity.setContentId(video.getContentId());
                compressedUrlEntity.setSerialNumber(video.getSerialNumber());
                compressedUrlEntity.setSerialId(video.getSerialId());
                compressedUrlEntity.setStoragePath(r2M3u8Path);
                compressedUrlEntity.setCompressType("compressed");
                compressedUrlEntity.setVideoFormat("hls");
                compressedUrlEntity.setVideoUrl(compressedUrl);
                compressedUrlEntity.setCompressionSuccess(true);

                // 获取视频时长
                long videoDuration = videoCompress.getVideoDuration(tempInputPath);
                compressedUrlEntity.setVideoSeconds(videoDuration);

                // 设置过期时间
                Date expireDate = new Date(System.currentTimeMillis() + Duration.ofHours(URL_EXPIRY_HOURS).toMillis());
                compressedUrlEntity.setVideoUrlExpireTime(expireDate);

                appDramaContentsSerialUrlService.getMapper().insert(compressedUrlEntity);
                log.info("视频处理完成: {}, HLS压缩成功", originalUrl);

                // 清理临时文件
                cleanupTempFiles(tempInputPath, tempHlsDir);
                log.info("HLS压缩视频结束: cost {}ms", System.currentTimeMillis() - time);
            } catch (Exception e) {
                log.error("处理HLS视频失败: {}", video.getVideoUrl(), e);
            }
        }
    }

    /**
     * 处理未压缩的视频
     */
    private void processUncompressedVideos() {
        // 查询未压缩的视频
        QueryWrapper<AppDramaContentsSerial> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("serial_url")
                   .isNotNull("storage_path")
                   ;

        List<AppDramaContentsSerial> uncompressedVideos = appDramaContentsSerialService.getMapper().selectList(queryWrapper);

        for (AppDramaContentsSerial video : uncompressedVideos) {
            long time = System.currentTimeMillis();
            try {
                String originalUrl = video.getSerialUrl();
                String storagePath = video.getStoragePath();

                if (originalUrl == null || originalUrl.isEmpty() || storagePath == null || storagePath.isEmpty()) {
                    continue;
                }

                // 检查是否已存在mp4版本
                QueryWrapper<AppDramaContentsSerialUrl> urlQuery = new QueryWrapper<>();
                urlQuery.eq("serial_id", video.getId())
                       .eq("compress_type", "compressed")
                        .eq("video_format", "mp4")
                ;

                if (appDramaContentsSerialUrlService.getMapper().selectCount(urlQuery) > 0) {
                    continue;
                }

                // 下载原始视频
                String tempInputPath = downloadVideo(originalUrl);
                if (tempInputPath == null) {
                    continue;
                }

                // 生成压缩后的文件路径
                String compressedPath = generateCompressedPath(storagePath);
                String tempOutputPath = tempVideoPath + "/" + UUID.randomUUID() + ".mp4";

                // 压缩视频
                String compressedVideoPath = videoCompress.compressVideo(tempInputPath, tempOutputPath);

                // 检查压缩是否成功
                boolean compressionSuccess = false;
                File originalFile = new File(tempInputPath);
                File compressedFile = new File(compressedVideoPath);
                if (compressedFile.exists() && originalFile.exists()) {
                    // 如果压缩后的文件不等于原始文件，则认为压缩成功
                    if (compressedFile.length() != originalFile.length()) {
                        compressionSuccess = true;
                        log.info("视频压缩成功 原始大小: {} MB, 压缩后大小: {} MB",
                            originalFile.length() / (1024.0 * 1024.0),
                            compressedFile.length() / (1024.0 * 1024.0));
                    }
                }

                // 获取视频时长
                long videoDuration = videoCompress.getVideoDuration(tempInputPath);

                // 设置视频大小
                video.setVideoSize(originalFile.length());
                video.setVideoSeconds(videoDuration);
                appDramaContentsSerialService.getMapper().updateById(video);

                // 上传压缩后的视频到R2
                String compressedUrl = uploadCompressedVideo(compressedVideoPath, compressedPath);

                if (compressedUrl != null) {
                    // 创建压缩视频的URL记录
                    AppDramaContentsSerialUrl compressedUrlEntity = new AppDramaContentsSerialUrl();
                    compressedUrlEntity.setVideoSize(compressedFile.length());
                    compressedUrlEntity.setContentId(video.getContentId());
                    compressedUrlEntity.setSerialNumber(video.getSerialNumber());
                    compressedUrlEntity.setSerialId(video.getId().toString());
                    compressedUrlEntity.setStoragePath(compressedPath);
                    compressedUrlEntity.setCompressType("compressed");
                    compressedUrlEntity.setVideoFormat("mp4");
                    compressedUrlEntity.setVideoUrl(compressedUrl);
                    compressedUrlEntity.setCompressionSuccess(compressionSuccess); // 设置压缩成功标记
                    compressedUrlEntity.setVideoSeconds(videoDuration);

                    // 设置过期时间
                    Duration duration = Duration.ofHours(URL_EXPIRY_HOURS);
                    Date expireDate = new Date(System.currentTimeMillis() + duration.toMillis());
                    compressedUrlEntity.setVideoUrlExpireTime(expireDate);

                    appDramaContentsSerialUrlService.getMapper().insert(compressedUrlEntity);
                    log.info("视频处理完成: {}, 压缩{}", originalUrl, compressionSuccess ? "成功" : "未生效");
                }

                // 清理临时文件
                cleanupTempFiles(tempInputPath, tempOutputPath);
                log.info("压缩视频结束: cost {}ms", System.currentTimeMillis() - time);

            } catch (Exception e) {
                log.error("处理视频失败: {}", video.getSerialUrl(), e);
            }
        }
    }

    /**
     * 更新已压缩视频的时长
     */
    private void updateCompressedVideoDurations() {
        // 查询已压缩的mp4视频
        QueryWrapper<AppDramaContentsSerialUrl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compress_type", "compressed")
                .eq("video_format", "mp4")
                .eq("video_seconds", 0);  // 只处理未设置时长的视频

        List<AppDramaContentsSerialUrl> compressedVideos = appDramaContentsSerialUrlService.getMapper().selectList(queryWrapper);
        if (CollectionUtils.isEmpty(compressedVideos)) {
            return;
        }
        log.info("找到 {} 个需要更新时长的视频", compressedVideos.size());

        for (AppDramaContentsSerialUrl video : compressedVideos) {
            String tempPath = null;
            try {
                // 下载视频到临时目录
                tempPath = downloadVideo(video.getVideoUrl());
                if (tempPath == null) {
                    log.error("下载视频失败: {}", video.getVideoUrl());
                    continue;
                }

                // 获取视频时长
                long videoDuration = videoCompress.getVideoDuration(tempPath);
                log.info("视频时长: {}秒, serialId: {}", videoDuration, video.getSerialId());

                // 更新url表的时长
                video.setVideoSeconds(videoDuration);
                appDramaContentsSerialUrlService.getMapper().updateById(video);

                // 更新剧集表的时长
                AppDramaContentsSerial serial = new AppDramaContentsSerial();
                serial.setId(Long.parseLong(video.getSerialId()));
                serial.setVideoSeconds(videoDuration);
                appDramaContentsSerialService.getMapper().updateById(serial);

                log.info("更新视频时长成功: {}", video.getVideoUrl());
            } catch (Exception e) {
                log.error("更新视频时长失败: {}", video.getVideoUrl(), e);
            } finally {
                // 清理临时文件
                if (tempPath != null) {
                    cleanupTempFiles(tempPath);
                }
            }
        }
        log.info("更新视频时长完成");
    }

    /**
     * 下载视频到临时目录
     */
    private String downloadVideo(String url) {
        try {
            String tempPath = tempVideoPath + "/" + UUID.randomUUID().toString() + ".mp4";
            if (s3Uploader.downloadFile(url, tempPath)) {
                return tempPath;
            }
            return null;
        } catch (Exception e) {
            log.error("下载视频失败: {}", url, e);
            return null;
        }
    }


    /**
     * 生成压缩后的文件路径
     */
    private String generateCompressedPath(String originalPath) {
        // 从原始路径中提取目录和文件名
        String[] parts = originalPath.split("/");
        if (parts.length < 2) {
            return null;
        }

        // 构建新的路径：原始目录/compressed/文件名
        return parts[0] + "/" + COMPRESSED_DIR + "/" + parts[1];
    }

    /**
     * 上传压缩后的视频到R2
     */
    private String uploadCompressedVideo(String localPath, String remotePath) {
        try {
            providerOSS.uploadToPrivate(remotePath, localPath);
            Duration duration = Duration.ofHours(URL_EXPIRY_HOURS);
            return providerOSS.generateUrl(remotePath, duration);
        } catch (Exception e) {
            log.error("上传压缩视频失败: {}", remotePath, e);
            return null;
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(String... paths) {
        for (String path : paths) {
            try {
                if (path != null) {
                    new File(path).delete();
                }
            } catch (Exception e) {
                log.error("清理临时文件失败: {}", path, e);
            }
        }
    }
}
