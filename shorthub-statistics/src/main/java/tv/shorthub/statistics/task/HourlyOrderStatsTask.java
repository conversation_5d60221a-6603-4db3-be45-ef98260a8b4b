package tv.shorthub.statistics.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tv.shorthub.statistics.service.IHourlyOrderStatsService;

/**
 * 每小时订单统计定时任务
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Slf4j
@Component
public class HourlyOrderStatsTask {

    @Autowired
    private IHourlyOrderStatsService hourlyOrderStatsService;

    /**
     * 每10分钟执行一次，更新当前小时的订单统计数据
     * cron表达式: 0 0/10 * * * ? 表示每10分钟执行一次
     */
    @Scheduled(cron = "0/30 * * * * ?")
    public void updateCurrentHourOrderStats() {
        log.info("[订单统计定时任务] 开始执行每小时订单统计任务");

        try {
            hourlyOrderStatsService.updateCurrentHourOrderStats();
            log.info("[订单统计定时任务] 每小时订单统计任务执行完成");
        } catch (Exception e) {
            log.error("[订单统计定时任务] 执行失败: {}", e.getMessage(), e);
        }
    }


    @Scheduled(cron = "0 3 * * * ?")
    public void updatePreviousHourOrderStats() {
        log.info("[订单统计定时任务] 开始执行上一小时订单统计任务");

        try {
            // 获取上一小时的时间
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.add(java.util.Calendar.HOUR_OF_DAY, -1);
            calendar.set(java.util.Calendar.MINUTE, 0);
            calendar.set(java.util.Calendar.SECOND, 0);
            calendar.set(java.util.Calendar.MILLISECOND, 0);

            hourlyOrderStatsService.updateHourlyOrderStats(calendar.getTime());
            log.info("[订单统计定时任务] 上一小时订单统计任务执行完成");
        } catch (Exception e) {
            log.error("[订单统计定时任务] 上一小时统计执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日凌晨2:05执行，全量统计昨天一整天的数据，作为数据兜底
     * cron表达式: 0 5 2 * * ?
     */
    @Scheduled(cron = "0 5 2 * * ?")
    public void batchUpdateYesterdayStats() {
        log.info("[订单统计每日兜底任务] 开始执行");
        try {
            hourlyOrderStatsService.batchUpdateRecentOrderStats(1);
            log.info("[订单统计每日兜底任务] 成功完成");
        } catch (Exception e) {
            log.error("[订单统计每日兜底任务] 执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周日凌晨3:05执行，全量重算所有历史数据
     * cron表达式: 0 5 3 ? * SUN
     */
    @Scheduled(cron = "0 5 3 ? * SUN")
    public void batchUpdateAllStats() {
        log.info("[订单统计每周全量任务] 开始执行");
        try {
            String result = hourlyOrderStatsService.batchUpdateAllStats();
            log.info("[订单统计每周全量任务] 执行完成: {}", result);
        } catch (Exception e) {
            log.error("[订单统计每周全量任务] 执行失败: {}", e.getMessage(), e);
        }
    }
}
